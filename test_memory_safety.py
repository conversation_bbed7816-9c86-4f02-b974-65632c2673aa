#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
内存安全测试脚本
验证5G日志清洗工具在内存受限环境下的表现
"""

import os
import tempfile
import psutil
from pathlib import Path
from memory_optimized_5g_cleaner import OptimizedLog<PERSON>lean<PERSON>

def create_large_test_file(file_path: str, line_count: int = 100000):
    """创建大型测试日志文件"""
    sample_logs = [
        "2024-01-15 10:30:15 [INFO] AMF: UE Registration Request received from SUPI=123456789012345",
        "2024-01-15 10:30:16 [DEBUG] SMF: PDU Session Establishment Request processing for session_id=abc123",
        "2024-01-15 10:30:17 [ERROR] UPF: Connection failed to remote endpoint 192.168.1.100 - timeout after 30s",
        "2024-01-15 10:30:18 [WARN] AUSF: Authentication timeout for UE IMSI=460001234567890, retrying...",
        "2024-01-15 10:30:19 [INFO] PCF: Policy rule applied for session ID abc-def-123, QoS profile updated",
        "2024-01-15 10:30:20 [ERROR] AMF: Handover procedure failed - resource exhaustion on target gNB",
        "2024-01-15 10:30:21 [INFO] NRF: Service registration successful for SMF instance smf-001.5gc.mnc001.mcc460.3gppnetwork.org",
        "2024-01-15 10:30:22 [DEBUG] UDM: Subscriber data retrieval completed for SUPI=imsi-460001234567890",
        "2024-01-15 10:30:23 [FATAL] Network interface N2 connection lost - all UE contexts affected",
        "2024-01-15 10:30:24 [INFO] NGAP: UE context setup complete for UE_ID=12345, establishing radio bearers",
        "2024-01-15 10:30:25 [DEBUG] Routine heartbeat check - all network functions responding normally",
        "2024-01-15 10:30:26 [INFO] Background maintenance task completed successfully",
        "2024-01-15 10:30:27 [DEBUG] Memory usage check: 45% of allocated heap in use",
        "2024-01-15 10:30:28 [INFO] Periodic statistics collection: 1250 active sessions",
        "2024-01-15 10:30:29 [DEBUG] Cache cleanup completed, freed 128MB of memory",
    ]
    
    print(f"   创建测试文件 {Path(file_path).name} ({line_count:,} 行)...")
    with open(file_path, 'w', encoding='utf-8') as f:
        for i in range(line_count):
            log_line = sample_logs[i % len(sample_logs)]
            # 添加变化使每行略有不同
            log_line = log_line.replace("10:30:15", f"10:{30 + i//3600}:{15 + (i%3600)//60}")
            log_line = log_line.replace("123456789012345", f"{123456789012345 + i}")
            f.write(f"{log_line}\n")

def test_memory_usage():
    """测试内存使用情况"""
    print("🧪 内存使用测试")
    print("="*50)
    
    # 获取初始内存
    process = psutil.Process()
    initial_memory = process.memory_info().rss / (1024**3)
    print(f"初始内存使用: {initial_memory:.2f}GB")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        input_dir = Path(temp_dir) / "input"
        output_dir = Path(temp_dir) / "output"
        
        input_dir.mkdir()
        output_dir.mkdir()
        
        # 创建多个大文件模拟真实场景
        test_files = []
        file_sizes = [50000, 100000, 150000, 80000, 120000]  # 不同大小的文件
        
        for i, size in enumerate(file_sizes):
            file_path = input_dir / f"large_test_{i+1}.txt"
            create_large_test_file(str(file_path), size)
            test_files.append(file_path)
        
        # 检查文件创建后的内存
        after_creation_memory = process.memory_info().rss / (1024**3)
        print(f"文件创建后内存: {after_creation_memory:.2f}GB")
        
        # 构建文件列表
        file_list = []
        for test_file in test_files:
            output_file = output_dir / test_file.name
            file_list.append((str(test_file), str(output_file)))
        
        print(f"\n开始处理 {len(file_list)} 个大文件...")
        
        # 初始化清洗器并处理
        cleaner = OptimizedLogCleaner()
        results = cleaner.process_files(file_list)
        
        # 检查处理后的内存
        final_memory = process.memory_info().rss / (1024**3)
        peak_memory = results['peak_memory_gb']
        
        print(f"\n内存使用分析:")
        print(f"   初始内存: {initial_memory:.2f}GB")
        print(f"   峰值内存: {peak_memory:.2f}GB")
        print(f"   最终内存: {final_memory:.2f}GB")
        print(f"   内存增长: {final_memory - initial_memory:.2f}GB")
        
        # 验证内存安全
        memory_safe = peak_memory < 12.0  # 12GB限制
        print(f"\n内存安全检查: {'✅ 通过' if memory_safe else '❌ 失败'}")
        
        if not memory_safe:
            print(f"   警告: 峰值内存 {peak_memory:.2f}GB 超过 12GB 限制")
        
        return memory_safe, results

def test_large_file_handling():
    """测试超大文件处理"""
    print("\n🧪 超大文件处理测试")
    print("="*50)
    
    with tempfile.TemporaryDirectory() as temp_dir:
        input_dir = Path(temp_dir) / "input"
        output_dir = Path(temp_dir) / "output"
        
        input_dir.mkdir()
        output_dir.mkdir()
        
        # 创建一个超大文件（30万行，模拟真实的大日志文件）
        huge_file = input_dir / "huge_test.txt"
        create_large_test_file(str(huge_file), 300000)
        
        file_size_mb = huge_file.stat().st_size / (1024**2)
        print(f"超大文件大小: {file_size_mb:.1f}MB")
        
        # 处理超大文件
        cleaner = OptimizedLogCleaner()
        file_list = [(str(huge_file), str(output_dir / huge_file.name))]
        
        results = cleaner.process_files(file_list)
        
        # 检查结果
        output_file = output_dir / huge_file.name
        if output_file.exists():
            output_size_mb = output_file.stat().st_size / (1024**2)
            compression_ratio = (1 - output_size_mb / file_size_mb) * 100
            
            print(f"处理结果:")
            print(f"   原始大小: {file_size_mb:.1f}MB")
            print(f"   处理后大小: {output_size_mb:.1f}MB")
            print(f"   压缩率: {compression_ratio:.1f}%")
            print(f"   峰值内存: {results['peak_memory_gb']:.2f}GB")
            
            return results['peak_memory_gb'] < 12.0
        else:
            print("❌ 超大文件处理失败")
            return False

def test_encoding_handling():
    """测试编码处理"""
    print("\n🧪 编码处理测试")
    print("="*50)
    
    with tempfile.TemporaryDirectory() as temp_dir:
        input_dir = Path(temp_dir) / "input"
        output_dir = Path(temp_dir) / "output"
        
        input_dir.mkdir()
        output_dir.mkdir()
        
        # 创建不同编码的文件
        encodings_to_test = ['utf-8', 'gbk']
        test_files = []
        
        for encoding in encodings_to_test:
            file_path = input_dir / f"test_{encoding}.txt"
            
            # 创建包含中文的日志
            chinese_logs = [
                "2024-01-15 10:30:15 [错误] AMF: 用户注册请求失败",
                "2024-01-15 10:30:16 [信息] SMF: PDU会话建立成功",
                "2024-01-15 10:30:17 [警告] UPF: 连接超时，正在重试",
            ]
            
            with open(file_path, 'w', encoding=encoding) as f:
                for i in range(1000):
                    log_line = chinese_logs[i % len(chinese_logs)]
                    f.write(f"{log_line}\n")
            
            test_files.append(file_path)
        
        # 处理不同编码的文件
        cleaner = OptimizedLogCleaner()
        file_list = [(str(f), str(output_dir / f.name)) for f in test_files]
        
        results = cleaner.process_files(file_list)
        
        success_rate = results['processed_files'] / results['total_files']
        print(f"编码处理成功率: {success_rate*100:.1f}%")
        
        return success_rate > 0.8  # 80%以上成功率

def main():
    """运行所有测试"""
    print("🚀 5G日志清洗工具内存安全测试")
    print("="*60)
    
    # 显示系统信息
    memory_total = psutil.virtual_memory().total / (1024**3)
    cpu_count = psutil.cpu_count()
    print(f"系统配置: {memory_total:.1f}GB内存, {cpu_count}核CPU")
    print(f"测试目标: 验证在16GB内存环境下的安全性")
    
    tests = [
        ("内存使用测试", test_memory_usage),
        ("超大文件处理测试", test_large_file_handling),
        ("编码处理测试", test_encoding_handling),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*60}")
        try:
            result = test_func()
            if result:
                print(f"✅ {test_name} - 通过")
                passed += 1
            else:
                print(f"❌ {test_name} - 失败")
        except Exception as e:
            print(f"❌ {test_name} - 异常: {e}")
    
    print(f"\n{'='*60}")
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！工具可以安全部署到16GB内存服务器")
    else:
        print("⚠️  部分测试失败，建议检查内存配置")
    
    print("="*60)

if __name__ == "__main__":
    main()
