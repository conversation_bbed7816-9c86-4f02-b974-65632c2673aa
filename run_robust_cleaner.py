#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
运行稳健版5G核心网日志清洗工具

使用方法:
python run_robust_cleaner.py
"""

import sys
import os
from pathlib import Path
from robust_5g_cleaner import RobustLogCleaner

def main():
    """主函数"""
    
    # 配置路径 - 请根据实际情况修改
    input_dir = "files/train"      # 输入目录
    output_dir = "files/cleaned"   # 输出目录
    
    print("="*60)
    print("🚀 5G核心网日志清洗工具 - 稳健版")
    print("="*60)
    
    # 检查输入目录
    if not Path(input_dir).exists():
        print(f"❌ 输入目录不存在: {input_dir}")
        print("请修改 run_robust_cleaner.py 中的 input_dir 路径")
        return
    
    # 创建清洗器
    cleaner = RobustLogCleaner()
    
    # 显示系统信息
    memory_info = cleaner.memory_monitor.get_memory_info()
    print(f"💻 系统内存: {memory_info['system_available_gb']:.1f}GB 可用")
    print(f"⚙️  内存限制: {8.0}GB (保守设置)")
    print(f"📁 输入目录: {input_dir}")
    print(f"📁 输出目录: {output_dir}")
    
    # 确认开始
    try:
        response = input(f"\n开始处理? (y/N): ")
        if response.lower() != 'y':
            print("❌ 已取消")
            return
    except KeyboardInterrupt:
        print("\n❌ 已取消")
        return
    
    try:
        # 开始处理
        results = cleaner.process_files_robust(
            input_dir=input_dir,
            output_dir=output_dir,
            file_pattern="*.txt"
        )
        
        # 检查结果
        if "error" in results:
            print(f"❌ 处理出错: {results['error']}")
            return
        
        # 显示最终结果
        print("\n" + "="*60)
        print("🎉 处理结果汇总")
        print("="*60)
        
        completion_rate = results['completion_rate']
        if completion_rate >= 95:
            print(f"✅ 处理成功! 完成率: {completion_rate:.1f}%")
        elif completion_rate >= 80:
            print(f"⚠️  部分完成: {completion_rate:.1f}%")
        else:
            print(f"❌ 处理不完整: {completion_rate:.1f}%")
        
        print(f"📊 详细统计:")
        print(f"   总文件数: {results['total_files']}")
        print(f"   已完成: {results['total_completed']}")
        print(f"   本次处理: {results['newly_processed']}")
        print(f"   失败: {results['failed_files']}")
        
        if results['failed_files'] > 0:
            print(f"\n❌ 失败文件 ({results['failed_files']} 个):")
            for failed in results.get('failed_files_list', [])[:10]:
                print(f"   - {failed}")
        
        # 给出后续建议
        if completion_rate < 100:
            print(f"\n💡 建议:")
            print(f"   - 可以重新运行此脚本继续处理剩余文件")
            print(f"   - 已处理的文件会自动跳过（断点续传）")
            if results['failed_files'] > 0:
                print(f"   - 检查失败文件的具体错误原因")
        
    except KeyboardInterrupt:
        print(f"\n⚠️  用户中断处理")
        print(f"已处理的文件已保存，可重新运行继续处理")
    except Exception as e:
        print(f"\n❌ 发生异常: {e}")
        import traceback
        traceback.print_exc()

def test_single_file():
    """测试单个文件处理"""
    print("🧪 测试模式 - 处理单个文件")
    
    # 找到第一个txt文件进行测试
    input_dir = Path("files/train")
    if not input_dir.exists():
        print(f"❌ 测试目录不存在: {input_dir}")
        return
    
    txt_files = list(input_dir.glob("*.txt"))
    if not txt_files:
        print(f"❌ 没有找到txt文件")
        return
    
    test_file = txt_files[0]
    output_file = Path("test_output.txt")
    
    print(f"测试文件: {test_file.name}")
    
    cleaner = RobustLogCleaner()
    result = cleaner._process_single_file_safe(str(test_file), str(output_file))
    
    print(f"处理结果: {result}")
    if result == 'success' and output_file.exists():
        print(f"输出文件大小: {output_file.stat().st_size} 字节")
        print(f"输出文件: {output_file}")

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "test":
        test_single_file()
    else:
        main()
