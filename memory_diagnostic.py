#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
内存诊断工具
用于分析5G日志处理过程中的内存使用情况
"""

import psutil
import os
import gc
import sys
import time
from pathlib import Path
import tracemalloc

class MemoryDiagnostic:
    """内存诊断工具"""
    
    def __init__(self):
        self.process = psutil.Process()
        tracemalloc.start()
    
    def get_system_memory_info(self):
        """获取系统内存信息"""
        memory = psutil.virtual_memory()
        swap = psutil.swap_memory()
        
        print("="*50)
        print("🖥️  系统内存状态")
        print("="*50)
        print(f"总内存: {memory.total / (1024**3):.1f} GB")
        print(f"可用内存: {memory.available / (1024**3):.1f} GB")
        print(f"已使用: {memory.used / (1024**3):.1f} GB ({memory.percent:.1f}%)")
        print(f"空闲内存: {memory.free / (1024**3):.1f} GB")
        print(f"缓存: {memory.cached / (1024**3):.1f} GB")
        print(f"缓冲区: {memory.buffers / (1024**3):.1f} GB")
        
        print(f"\n💾 交换空间:")
        print(f"总交换: {swap.total / (1024**3):.1f} GB")
        print(f"已用交换: {swap.used / (1024**3):.1f} GB ({swap.percent:.1f}%)")
        
        # 内存压力评估
        if memory.percent > 90:
            print("⚠️  内存压力: 极高 (>90%)")
        elif memory.percent > 80:
            print("⚠️  内存压力: 高 (>80%)")
        elif memory.percent > 70:
            print("⚠️  内存压力: 中等 (>70%)")
        else:
            print("✅ 内存压力: 正常")
    
    def get_process_memory_info(self):
        """获取当前进程内存信息"""
        try:
            memory_info = self.process.memory_info()
            memory_percent = self.process.memory_percent()
            
            print("\n" + "="*50)
            print("🔍 当前进程内存状态")
            print("="*50)
            print(f"RSS (物理内存): {memory_info.rss / (1024**3):.3f} GB")
            print(f"VMS (虚拟内存): {memory_info.vms / (1024**3):.3f} GB")
            print(f"内存占用率: {memory_percent:.2f}%")
            
            # 获取tracemalloc信息
            current, peak = tracemalloc.get_traced_memory()
            print(f"Python跟踪内存 (当前): {current / (1024**2):.1f} MB")
            print(f"Python跟踪内存 (峰值): {peak / (1024**2):.1f} MB")
            
            # 获取详细内存映射（如果可用）
            try:
                memory_maps = self.process.memory_maps()
                total_mapped = sum(m.rss for m in memory_maps if m.rss) / (1024**3)
                print(f"内存映射总计: {total_mapped:.3f} GB")
            except (psutil.AccessDenied, AttributeError):
                print("内存映射信息: 无法访问")
                
        except Exception as e:
            print(f"获取进程内存信息失败: {e}")
    
    def analyze_file_sizes(self, directory: str):
        """分析文件大小分布"""
        path = Path(directory)
        if not path.exists():
            print(f"❌ 目录不存在: {directory}")
            return
        
        txt_files = list(path.glob("*.txt"))
        if not txt_files:
            print(f"❌ 没有找到txt文件")
            return
        
        print(f"\n" + "="*50)
        print(f"📁 文件大小分析: {directory}")
        print("="*50)
        
        file_sizes = []
        total_size = 0
        
        for file in txt_files:
            try:
                size = file.stat().st_size
                file_sizes.append(size)
                total_size += size
            except Exception as e:
                print(f"无法获取文件大小: {file.name} - {e}")
        
        if not file_sizes:
            return
        
        file_sizes.sort()
        
        print(f"文件总数: {len(file_sizes)}")
        print(f"总大小: {total_size / (1024**3):.2f} GB")
        print(f"平均大小: {(total_size / len(file_sizes)) / (1024**2):.1f} MB")
        print(f"最小文件: {min(file_sizes) / (1024**2):.1f} MB")
        print(f"最大文件: {max(file_sizes) / (1024**2):.1f} MB")
        print(f"中位数: {file_sizes[len(file_sizes)//2] / (1024**2):.1f} MB")
        
        # 大文件统计
        large_files = [s for s in file_sizes if s > 50 * 1024 * 1024]  # >50MB
        huge_files = [s for s in file_sizes if s > 100 * 1024 * 1024]  # >100MB
        
        print(f"\n大文件统计:")
        print(f"超过50MB: {len(large_files)} 个")
        print(f"超过100MB: {len(huge_files)} 个")
        
        if huge_files:
            print(f"⚠️  发现超大文件，可能导致内存问题")
    
    def test_memory_allocation(self, test_size_mb: int = 100):
        """测试内存分配"""
        print(f"\n" + "="*50)
        print(f"🧪 内存分配测试 ({test_size_mb}MB)")
        print("="*50)
        
        # 记录初始状态
        initial_memory = self.process.memory_info().rss / (1024**3)
        print(f"初始内存: {initial_memory:.3f} GB")
        
        try:
            # 分配内存
            test_data = 'x' * (test_size_mb * 1024 * 1024)
            
            # 检查分配后内存
            after_alloc = self.process.memory_info().rss / (1024**3)
            print(f"分配后内存: {after_alloc:.3f} GB")
            print(f"内存增长: {(after_alloc - initial_memory) * 1024:.1f} MB")
            
            # 释放内存
            del test_data
            gc.collect()
            
            # 等待一下让系统回收
            time.sleep(1)
            
            # 检查释放后内存
            after_gc = self.process.memory_info().rss / (1024**3)
            print(f"释放后内存: {after_gc:.3f} GB")
            print(f"内存回收: {(after_alloc - after_gc) * 1024:.1f} MB")
            
            if after_gc > initial_memory + 0.01:  # 10MB容差
                print(f"⚠️  可能存在内存泄漏: {(after_gc - initial_memory) * 1024:.1f} MB")
            else:
                print(f"✅ 内存回收正常")
                
        except MemoryError:
            print(f"❌ 内存分配失败: 无法分配 {test_size_mb}MB")
        except Exception as e:
            print(f"❌ 测试异常: {e}")
    
    def monitor_memory_for_duration(self, duration_seconds: int = 30):
        """监控内存使用一段时间"""
        print(f"\n" + "="*50)
        print(f"📊 内存监控 ({duration_seconds}秒)")
        print("="*50)
        
        start_time = time.time()
        memory_readings = []
        
        try:
            while time.time() - start_time < duration_seconds:
                memory_gb = self.process.memory_info().rss / (1024**3)
                memory_readings.append(memory_gb)
                print(f"时间: {time.time() - start_time:6.1f}s | 内存: {memory_gb:.3f} GB")
                time.sleep(2)
                
        except KeyboardInterrupt:
            print("\n监控被中断")
        
        if memory_readings:
            print(f"\n监控结果:")
            print(f"平均内存: {sum(memory_readings) / len(memory_readings):.3f} GB")
            print(f"最小内存: {min(memory_readings):.3f} GB")
            print(f"最大内存: {max(memory_readings):.3f} GB")
            print(f"内存波动: {max(memory_readings) - min(memory_readings):.3f} GB")
    
    def get_memory_recommendations(self):
        """获取内存优化建议"""
        memory = psutil.virtual_memory()
        
        print(f"\n" + "="*50)
        print(f"💡 内存优化建议")
        print("="*50)
        
        if memory.percent > 80:
            print("⚠️  系统内存使用率过高，建议:")
            print("   - 关闭不必要的程序")
            print("   - 减少批处理大小")
            print("   - 降低内存限制阈值")
        
        if memory.available < 4 * 1024**3:  # 4GB
            print("⚠️  可用内存不足4GB，建议:")
            print("   - 设置更保守的内存限制 (6GB)")
            print("   - 使用更小的文件块大小")
            print("   - 启用更频繁的垃圾回收")
        
        print("\n📋 推荐配置:")
        available_gb = memory.available / (1024**3)
        recommended_limit = max(4, min(8, available_gb * 0.6))
        
        print(f"   MEMORY_LIMIT_GB = {recommended_limit:.1f}")
        print(f"   MEMORY_WARNING_GB = {recommended_limit * 0.8:.1f}")
        print(f"   BATCH_SIZE = {3 if available_gb < 8 else 5}")
        print(f"   MAX_LINES_PER_FILE = {1500 if available_gb < 8 else 2000}")

def main():
    """主函数"""
    diagnostic = MemoryDiagnostic()
    
    print("🔍 5G日志处理内存诊断工具")
    
    # 基础内存信息
    diagnostic.get_system_memory_info()
    diagnostic.get_process_memory_info()
    
    # 分析文件大小
    if Path("files/train").exists():
        diagnostic.analyze_file_sizes("files/train")
    else:
        print(f"\n⚠️  未找到 files/train 目录，跳过文件分析")
    
    # 内存分配测试
    diagnostic.test_memory_allocation(50)  # 测试50MB分配
    
    # 获取建议
    diagnostic.get_memory_recommendations()
    
    # 询问是否进行监控
    try:
        response = input(f"\n是否进行30秒内存监控? (y/N): ")
        if response.lower() == 'y':
            diagnostic.monitor_memory_for_duration(30)
    except KeyboardInterrupt:
        pass
    
    print(f"\n诊断完成!")

if __name__ == "__main__":
    main()
