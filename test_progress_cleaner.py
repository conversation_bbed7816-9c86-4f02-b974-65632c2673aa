#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试进度增强的5G日志清洗工具
"""

import os
import tempfile
import shutil
from pathlib import Path
from progress_enhanced_cleaner import ProgressEnhancedLogCleaner

def create_test_log_file(file_path: str, line_count: int = 1000):
    """创建测试日志文件"""
    sample_logs = [
        "2024-01-15 10:30:15 [INFO] AMF: UE Registration Request received from SUPI=123456789012345",
        "2024-01-15 10:30:16 [DEBUG] SMF: PDU Session Establishment Request processing",
        "2024-01-15 10:30:17 [ERROR] UPF: Connection failed to remote endpoint 192.168.1.100",
        "2024-01-15 10:30:18 [WARN] AUSF: Authentication timeout for UE IMSI=460001234567890",
        "2024-01-15 10:30:19 [INFO] PCF: Policy rule applied for session ID abc-def-123",
        "2024-01-15 10:30:20 [ERROR] AMF: Handover procedure failed - resource exhaustion",
        "2024-01-15 10:30:21 [INFO] NRF: Service registration successful for SMF instance",
        "2024-01-15 10:30:22 [DEBUG] UDM: Subscriber data retrieval completed",
        "2024-01-15 10:30:23 [FATAL] Network interface N2 connection lost",
        "2024-01-15 10:30:24 [INFO] NGAP: UE context setup complete",
    ]
    
    with open(file_path, 'w', encoding='utf-8') as f:
        for i in range(line_count):
            log_line = sample_logs[i % len(sample_logs)]
            # 添加一些变化
            log_line = log_line.replace("10:30:15", f"10:30:{15 + i % 45}")
            f.write(f"{log_line}\n")

def test_small_dataset():
    """测试小数据集"""
    print("🧪 测试小数据集处理...")
    
    # 创建临时目录
    with tempfile.TemporaryDirectory() as temp_dir:
        input_dir = Path(temp_dir) / "input"
        output_dir = Path(temp_dir) / "output"
        
        input_dir.mkdir()
        output_dir.mkdir()
        
        # 创建3个测试文件
        test_files = []
        for i in range(3):
            file_path = input_dir / f"test_{i+1}.txt"
            create_test_log_file(str(file_path), line_count=500 + i * 200)
            test_files.append(file_path)
        
        print(f"   创建了 {len(test_files)} 个测试文件")
        
        # 初始化清洗器
        cleaner = ProgressEnhancedLogCleaner()
        
        # 构建文件列表
        file_list = []
        for test_file in test_files:
            output_file = output_dir / test_file.name
            file_list.append((str(test_file), str(output_file)))
        
        # 处理文件
        print("   开始处理...")
        results = cleaner.process_files_with_detailed_progress(
            file_list, str(output_dir), max_workers=2
        )
        
        # 生成报告
        report = cleaner.generate_detailed_report(str(output_dir))
        
        # 验证结果
        print(f"   ✅ 处理完成:")
        print(f"      成功: {results['processed_files']} 个文件")
        print(f"      失败: {results['failed_files']} 个文件")
        print(f"      处理时间: {results['processing_time']:.2f} 秒")
        
        # 检查输出文件
        output_files = list(output_dir.glob("*.txt"))
        print(f"      输出文件: {len(output_files)} 个")
        
        return results['processed_files'] == len(test_files)

def test_large_file():
    """测试大文件处理"""
    print("🧪 测试大文件处理...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        input_dir = Path(temp_dir) / "input"
        output_dir = Path(temp_dir) / "output"
        
        input_dir.mkdir()
        output_dir.mkdir()
        
        # 创建一个大文件（10万行）
        large_file = input_dir / "large_test.txt"
        print("   创建大文件（10万行）...")
        create_test_log_file(str(large_file), line_count=100000)
        
        file_size_mb = large_file.stat().st_size / (1024 * 1024)
        print(f"   文件大小: {file_size_mb:.1f} MB")
        
        # 初始化清洗器
        cleaner = ProgressEnhancedLogCleaner()
        
        # 处理文件
        file_list = [(str(large_file), str(output_dir / large_file.name))]
        
        print("   开始处理大文件...")
        results = cleaner.process_files_with_detailed_progress(
            file_list, str(output_dir), max_workers=1
        )
        
        # 生成报告
        report = cleaner.generate_detailed_report(str(output_dir))
        
        print(f"   ✅ 大文件处理完成:")
        print(f"      处理时间: {results['processing_time']:.2f} 秒")
        print(f"      处理行数: {cleaner.stats.total_lines_processed:,}")
        print(f"      保留行数: {cleaner.stats.total_lines_kept:,}")
        print(f"      压缩率: {report['data_statistics']['compression_ratio']:.1f}%")
        
        return results['processed_files'] == 1

def test_error_handling():
    """测试错误处理"""
    print("🧪 测试错误处理...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        input_dir = Path(temp_dir) / "input"
        output_dir = Path(temp_dir) / "output"
        
        input_dir.mkdir()
        output_dir.mkdir()
        
        # 创建一个正常文件和一个不存在的文件
        normal_file = input_dir / "normal.txt"
        create_test_log_file(str(normal_file), 100)
        
        missing_file = input_dir / "missing.txt"  # 不创建这个文件
        
        # 初始化清洗器
        cleaner = ProgressEnhancedLogCleaner()
        
        # 文件列表包含存在和不存在的文件
        file_list = [
            (str(normal_file), str(output_dir / "normal.txt")),
            (str(missing_file), str(output_dir / "missing.txt")),
        ]
        
        print("   处理包含错误的文件列表...")
        results = cleaner.process_files_with_detailed_progress(
            file_list, str(output_dir), max_workers=2
        )
        
        print(f"   ✅ 错误处理测试完成:")
        print(f"      成功: {results['processed_files']} 个文件")
        print(f"      失败: {results['failed_files']} 个文件")
        print(f"      失败详情: {len(results['failed_file_details'])} 条记录")
        
        return results['failed_files'] == 1 and results['processed_files'] == 1

def main():
    """运行所有测试"""
    print("=" * 60)
    print("🚀 5G日志清洗工具进度功能测试")
    print("=" * 60)
    
    tests = [
        ("小数据集处理", test_small_dataset),
        ("大文件处理", test_large_file),
        ("错误处理", test_error_handling),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}:")
        try:
            result = test_func()
            if result:
                print(f"   ✅ {test_name} - 通过")
                passed += 1
            else:
                print(f"   ❌ {test_name} - 失败")
        except Exception as e:
            print(f"   ❌ {test_name} - 异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！工具已准备好处理您的188个文件")
    else:
        print("⚠️  部分测试失败，请检查代码")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
