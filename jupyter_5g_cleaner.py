#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
5G核心网日志清洗工具 - Jupyter Notebook版本
内存优化，适用于16GB内存环境
"""

import os
import gc
import time
import psutil
from pathlib import Path
from typing import List, Dict, Tuple, Optional
import re

# 内存安全配置
MEMORY_LIMIT_GB = 12.0
MEMORY_WARNING_GB = 10.0
CHUNK_SIZE = 512 * 1024
MAX_LINES_PER_FILE = 3000
MAX_CHARS_PER_FILE = 300000

class MemoryMonitor:
    """轻量级内存监控器"""
    def __init__(self):
        self.process = psutil.Process()
        self.peak_memory = 0
        
    def get_memory_gb(self) -> float:
        """获取当前内存使用量(GB)"""
        try:
            memory_gb = self.process.memory_info().rss / (1024**3)
            self.peak_memory = max(self.peak_memory, memory_gb)
            return memory_gb
        except:
            return 0.0
    
    def check_memory_safe(self) -> bool:
        """检查内存是否安全"""
        current_memory = self.get_memory_gb()
        return current_memory <= MEMORY_LIMIT_GB
    
    def force_gc(self):
        """强制垃圾回收"""
        gc.collect()

class OptimizedLogCleaner:
    """内存优化的5G日志清洗器"""
    
    def __init__(self):
        self.memory_monitor = MemoryMonitor()
        self.stats = {
            'total_files': 0,
            'processed_files': 0,
            'failed_files': 0,
            'total_lines_processed': 0,
            'total_lines_kept': 0,
            'start_time': None,
            'failed_files_list': []
        }
        self._compile_patterns()
    
    def _compile_patterns(self):
        """编译关键的正则表达式模式"""
        g5_nf = 'AMF|SMF|UPF|AUSF|UDM|UDR|PCF|NRF|NSSF|NEF'
        
        self.patterns = {
            'g5_nf': re.compile(rf'\b({g5_nf})\b', re.IGNORECASE),
            'error': re.compile(r'\[(ERROR|FATAL|CRITICAL)\]', re.IGNORECASE),
            'warning': re.compile(r'\[(WARN|WARNING)\]', re.IGNORECASE),
            'timestamp': re.compile(r'\d{4}-\d{2}-\d{2}[T ]\d{2}:\d{2}:\d{2}'),
            'fault_keywords': re.compile(
                r'(failed|failure|error|timeout|exception|denied|reject|abort|'
                r'connection.*failed|authentication.*failed|resource.*exhausted)',
                re.IGNORECASE
            )
        }
    
    def _detect_encoding_fast(self, file_path: str) -> str:
        """快速编码检测"""
        try:
            with open(file_path, 'rb') as f:
                sample = f.read(1024)
            
            try:
                sample.decode('utf-8')
                return 'utf-8'
            except UnicodeDecodeError:
                pass
            
            try:
                sample.decode('gbk')
                return 'gbk'
            except UnicodeDecodeError:
                pass
                
            return 'latin1'
        except:
            return 'utf-8'
    
    def _read_file_in_chunks(self, file_path: str) -> Optional[str]:
        """分块读取文件，内存安全"""
        try:
            encoding = self._detect_encoding_fast(file_path)
            file_size = os.path.getsize(file_path)
            
            if file_size > 50 * 1024 * 1024:  # 50MB
                return self._read_large_file_limited(file_path, encoding)
            
            with open(file_path, 'r', encoding=encoding, buffering=CHUNK_SIZE) as f:
                return f.read()
                
        except Exception as e:
            return None
    
    def _read_large_file_limited(self, file_path: str, encoding: str) -> str:
        """限制读取大文件的前部分内容"""
        content_parts = []
        total_read = 0
        max_read = 30 * 1024 * 1024  # 最多读取30MB
        
        try:
            with open(file_path, 'r', encoding=encoding) as f:
                while total_read < max_read:
                    chunk = f.read(CHUNK_SIZE)
                    if not chunk:
                        break
                    content_parts.append(chunk)
                    total_read += len(chunk.encode(encoding))
                    
                    if not self.memory_monitor.check_memory_safe():
                        break
            
            return ''.join(content_parts)
        except Exception as e:
            return ""
    
    def _intelligent_line_sampling(self, lines: List[str]) -> List[str]:
        """内存优化的智能行采样"""
        if len(lines) <= MAX_LINES_PER_FILE:
            return [line.strip() for line in lines if line.strip()]
        
        critical_lines = []
        important_lines = []
        normal_lines = []
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            if self.patterns['error'].search(line) or self.patterns['fault_keywords'].search(line):
                critical_lines.append(line)
            elif self.patterns['g5_nf'].search(line) or self.patterns['warning'].search(line):
                important_lines.append(line)
            else:
                normal_lines.append(line)
        
        selected_lines = critical_lines[:]
        remaining_quota = MAX_LINES_PER_FILE - len(selected_lines)
        
        if remaining_quota > 0:
            important_count = min(len(important_lines), remaining_quota // 2)
            if important_count > 0:
                step = max(1, len(important_lines) // important_count)
                selected_lines.extend(important_lines[::step][:important_count])
                remaining_quota -= important_count
            
            if remaining_quota > 0 and normal_lines:
                normal_count = min(len(normal_lines), remaining_quota)
                step = max(1, len(normal_lines) // normal_count)
                selected_lines.extend(normal_lines[::step][:normal_count])
        
        return selected_lines
    
    def _clean_content(self, content: str) -> str:
        """清洗日志内容"""
        if not content or not content.strip():
            return ""
        
        content = re.sub(r'\r\n|\r', '\n', content)
        content = re.sub(r'[\x00-\x08\x0b\x0c\x0e-\x1f\x7f-\xff]', '', content)
        
        lines = content.split('\n')
        original_count = len(lines)
        
        cleaned_lines = self._intelligent_line_sampling(lines)
        
        content = '\n'.join(cleaned_lines)
        
        if len(content) > MAX_CHARS_PER_FILE:
            content = content[:MAX_CHARS_PER_FILE]
            last_newline = content.rfind('\n')
            if last_newline > 0:
                content = content[:last_newline]
        
        content = re.sub(
            r'(\d{4})-(\d{2})-(\d{2})T(\d{2}):(\d{2}):(\d{2})(?:\.\d+)?(?:Z|[+-]\d{2}:?\d{2})?',
            r'\1-\2-\3 \4:\5:\6', content
        )
        
        self.stats['total_lines_processed'] += original_count
        self.stats['total_lines_kept'] += len(cleaned_lines)
        
        return content
    
    def _process_single_file(self, input_file: str, output_file: str) -> bool:
        """处理单个文件"""
        try:
            if not self.memory_monitor.check_memory_safe():
                self.memory_monitor.force_gc()
                time.sleep(0.1)
            
            content = self._read_file_in_chunks(input_file)
            if not content:
                return False
            
            cleaned_content = self._clean_content(content)
            del content
            
            if cleaned_content.strip():
                os.makedirs(os.path.dirname(output_file), exist_ok=True)
                
                with open(output_file, 'w', encoding='utf-8') as f:
                    f.write(cleaned_content)
                
                del cleaned_content
                return True
            else:
                del cleaned_content
                return False
                
        except Exception as e:
            self.stats['failed_files_list'].append(f"{Path(input_file).name}: {str(e)}")
            return False
        finally:
            self.memory_monitor.force_gc()
    
    def process_files(self, input_dir: str, output_dir: str, file_pattern: str = "*.txt") -> Dict:
        """处理文件目录"""
        input_path = Path(input_dir)
        output_path = Path(output_dir)
        
        if not input_path.exists():
            return {"error": f"输入目录不存在: {input_dir}"}
        
        txt_files = list(input_path.glob(file_pattern))
        if not txt_files:
            return {"error": f"在 {input_dir} 中没有找到匹配 {file_pattern} 的文件"}
        
        output_path.mkdir(parents=True, exist_ok=True)
        
        file_list = [(str(f), str(output_path / f.name)) for f in txt_files]
        
        self.stats['total_files'] = len(file_list)
        self.stats['start_time'] = time.time()
        
        print(f"开始处理 {len(file_list)} 个文件...")
        
        for i, (input_file, output_file) in enumerate(file_list, 1):
            if i % 20 == 0 or i == len(file_list):
                progress = i / len(file_list) * 100
                memory_gb = self.memory_monitor.get_memory_gb()
                print(f"进度: {i}/{len(file_list)} ({progress:.1f}%) | 内存: {memory_gb:.1f}GB")
            
            if self._process_single_file(input_file, output_file):
                self.stats['processed_files'] += 1
            else:
                self.stats['failed_files'] += 1
            
            if i % 10 == 0:
                self.memory_monitor.force_gc()
        
        return self._get_results()
    
    def _get_results(self) -> Dict:
        """获取处理结果"""
        elapsed_time = time.time() - self.stats['start_time']
        
        return {
            'total_files': self.stats['total_files'],
            'processed_files': self.stats['processed_files'],
            'failed_files': self.stats['failed_files'],
            'success_rate': self.stats['processed_files'] / self.stats['total_files'] * 100 if self.stats['total_files'] > 0 else 0,
            'total_lines_processed': self.stats['total_lines_processed'],
            'total_lines_kept': self.stats['total_lines_kept'],
            'line_retention_rate': self.stats['total_lines_kept'] / self.stats['total_lines_processed'] * 100 if self.stats['total_lines_processed'] > 0 else 0,
            'processing_time_minutes': elapsed_time / 60,
            'peak_memory_gb': self.memory_monitor.peak_memory,
            'files_per_minute': self.stats['processed_files'] / (elapsed_time / 60) if elapsed_time > 0 else 0,
            'failed_files_count': len(self.stats['failed_files_list']),
            'failed_files_list': self.stats['failed_files_list'][:5]  # 只返回前5个失败文件
        }
    
    def reset_stats(self):
        """重置统计信息"""
        self.stats = {
            'total_files': 0,
            'processed_files': 0,
            'failed_files': 0,
            'total_lines_processed': 0,
            'total_lines_kept': 0,
            'start_time': None,
            'failed_files_list': []
        }
        self.memory_monitor.peak_memory = 0
