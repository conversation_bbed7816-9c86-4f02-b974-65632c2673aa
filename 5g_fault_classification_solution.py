#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
5G核心网日志故障分类竞赛解决方案
目标：优化Macro F1-score，处理类别不平衡问题

竞赛数据：
- 训练集：188个日志文件，8个故障类型（0-7）
- 测试集：100个日志文件
- 评价指标：多分类加权Macro F1-score
"""

import os
import re
import pandas as pd
import numpy as np
from pathlib import Path
from typing import Dict, List, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

# 机器学习相关
from sklearn.model_selection import StratifiedKFold, cross_val_score, GridSearchCV
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier, VotingClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.svm import SVC
from sklearn.metrics import classification_report, f1_score, confusion_matrix
from sklearn.utils.class_weight import compute_class_weight
from imblearn.over_sampling import SMOTE, ADASYN
from imblearn.combine import SMOTEENN
from imblearn.pipeline import Pipeline as ImbPipeline

# 深度学习相关（可选）
try:
    import lightgbm as lgb
    import xgboost as xgb
    ADVANCED_MODELS = True
except ImportError:
    ADVANCED_MODELS = False
    print("LightGBM/XGBoost not available, using sklearn models only")

# 文本处理
import jieba
from collections import Counter
import time
from datetime import datetime

class G5LogFeatureExtractor:
    """5G核心网日志特征提取器"""
    
    def __init__(self):
        self.scaler = StandardScaler()
        self.tfidf_vectorizer = None
        self.label_encoder = LabelEncoder()
        
        # 5G核心网关键模式
        self._compile_patterns()
        
    def _compile_patterns(self):
        """编译5G相关正则表达式模式"""
        # 5G网元
        self.g5_nf_pattern = re.compile(
            r'\b(AMF|SMF|UPF|AUSF|UDM|UDR|PCF|NRF|NSSF|NEF|SEPP|SCP|BSF|CHF|NWDAF)\b',
            re.IGNORECASE
        )
        
        # 日志级别
        self.log_level_pattern = re.compile(r'\[(TRACE|DEBUG|INFO|WARN|WARNING|ERROR|FATAL|CRITICAL)\]', re.IGNORECASE)
        
        # 时间戳
        self.timestamp_pattern = re.compile(r'\d{4}-\d{2}-\d{2}[T ]\d{2}:\d{2}:\d{2}')
        
        # 5G业务流程
        self.g5_procedure_pattern = re.compile(
            r'\b(Registration|Authentication|Authorization|Handover|PDU.*Session|Service.*Request)\b',
            re.IGNORECASE
        )
        
        # 故障关键词
        self.fault_patterns = {
            'connection_failure': re.compile(r'(connection.*failed|timeout|refused|reset|unreachable)', re.IGNORECASE),
            'authentication_failure': re.compile(r'(auth.*failed|invalid.*credential|unauthorized|forbidden)', re.IGNORECASE),
            'resource_exhaustion': re.compile(r'(out.*of.*memory|resource.*exhausted|quota.*exceeded|limit.*reached)', re.IGNORECASE),
            'protocol_error': re.compile(r'(protocol.*error|invalid.*message|malformed|decode.*failed)', re.IGNORECASE),
            'service_unavailable': re.compile(r'(service.*unavailable|server.*error|endpoint.*not.*found)', re.IGNORECASE),
            'network_error': re.compile(r'(network.*error|routing.*failed|interface.*down)', re.IGNORECASE),
            'configuration_error': re.compile(r'(config.*error|parameter.*invalid|missing.*config)', re.IGNORECASE),
            'performance_issue': re.compile(r'(slow.*response|high.*latency|performance.*degraded)', re.IGNORECASE)
        }
        
        # IP地址和会话ID
        self.ip_pattern = re.compile(r'\b(?:\d{1,3}\.){3}\d{1,3}\b')
        self.session_pattern = re.compile(r'[Ss]ession[_\-\s]*[Ii][Dd][:=]\s*([a-fA-F0-9\-]+)')
        
    def extract_statistical_features(self, content: str) -> Dict:
        """提取统计特征"""
        if not content:
            return self._get_empty_statistical_features()
        
        lines = content.split('\n')
        non_empty_lines = [line.strip() for line in lines if line.strip()]
        
        if not non_empty_lines:
            return self._get_empty_statistical_features()
        
        features = {}
        
        # 基础统计
        features['total_lines'] = len(non_empty_lines)
        features['total_chars'] = len(content)
        features['avg_line_length'] = np.mean([len(line) for line in non_empty_lines])
        features['max_line_length'] = max([len(line) for line in non_empty_lines])
        features['min_line_length'] = min([len(line) for line in non_empty_lines])
        features['unique_lines_ratio'] = len(set(non_empty_lines)) / len(non_empty_lines)
        
        # 日志级别分布
        log_levels = ['TRACE', 'DEBUG', 'INFO', 'WARN', 'WARNING', 'ERROR', 'FATAL', 'CRITICAL']
        content_upper = content.upper()
        
        for level in log_levels:
            count = content_upper.count(f'[{level}]')
            features[f'{level.lower()}_count'] = count
            features[f'{level.lower()}_ratio'] = count / len(non_empty_lines) if non_empty_lines else 0
        
        # 错误严重程度评分
        error_score = (features['error_count'] * 3 + features['fatal_count'] * 5 + 
                      features['critical_count'] * 5 + features['warning_count'] * 1)
        features['error_severity_score'] = error_score
        
        return features
    
    def extract_5g_network_features(self, content: str) -> Dict:
        """提取5G网络特征"""
        features = {}
        
        # 5G网元统计
        nf_matches = self.g5_nf_pattern.findall(content)
        features['g5_nf_count'] = len(nf_matches)
        features['g5_nf_types'] = len(set([nf.upper() for nf in nf_matches]))
        
        # 各个网元的出现次数
        nf_counter = Counter([nf.upper() for nf in nf_matches])
        for nf in ['AMF', 'SMF', 'UPF', 'AUSF', 'UDM', 'UDR', 'PCF', 'NRF']:
            features[f'{nf.lower()}_count'] = nf_counter.get(nf, 0)
        
        # 5G业务流程
        procedure_matches = self.g5_procedure_pattern.findall(content)
        features['g5_procedure_count'] = len(procedure_matches)
        features['g5_procedure_types'] = len(set(procedure_matches))
        
        # 网络相关特征
        ip_matches = self.ip_pattern.findall(content)
        features['ip_address_count'] = len(ip_matches)
        features['unique_ip_count'] = len(set(ip_matches))
        
        session_matches = self.session_pattern.findall(content)
        features['session_count'] = len(session_matches)
        features['unique_session_count'] = len(set(session_matches))
        
        return features
    
    def extract_fault_features(self, content: str) -> Dict:
        """提取故障相关特征"""
        features = {}
        total_lines = len(content.split('\n'))
        
        # 各类故障模式统计
        for fault_type, pattern in self.fault_patterns.items():
            matches = pattern.findall(content)
            features[f'fault_{fault_type}_count'] = len(matches)
            features[f'fault_{fault_type}_ratio'] = len(matches) / total_lines if total_lines > 0 else 0
        
        # 故障密度（故障关键词总数/总行数）
        total_fault_keywords = sum([features[f'fault_{ft}_count'] for ft in self.fault_patterns.keys()])
        features['fault_density'] = total_fault_keywords / total_lines if total_lines > 0 else 0
        
        # 故障多样性（出现的故障类型数量）
        fault_diversity = sum([1 for ft in self.fault_patterns.keys() if features[f'fault_{ft}_count'] > 0])
        features['fault_diversity'] = fault_diversity
        
        return features
    
    def extract_temporal_features(self, content: str) -> Dict:
        """提取时间相关特征"""
        features = {}
        
        # 时间戳统计
        timestamp_matches = self.timestamp_pattern.findall(content)
        features['timestamp_count'] = len(timestamp_matches)
        features['has_timestamps'] = len(timestamp_matches) > 0
        
        if timestamp_matches:
            # 时间跨度分析
            try:
                timestamps = []
                for ts in timestamp_matches[:10]:  # 只分析前10个时间戳，避免性能问题
                    # 标准化时间格式
                    ts_clean = re.sub(r'T', ' ', ts)
                    ts_clean = re.sub(r'\+.*$', '', ts_clean)  # 移除时区信息
                    try:
                        dt = datetime.strptime(ts_clean[:19], '%Y-%m-%d %H:%M:%S')
                        timestamps.append(dt)
                    except:
                        continue
                
                if len(timestamps) >= 2:
                    time_span = (max(timestamps) - min(timestamps)).total_seconds()
                    features['time_span_seconds'] = time_span
                    features['time_span_minutes'] = time_span / 60
                    features['avg_time_interval'] = time_span / (len(timestamps) - 1) if len(timestamps) > 1 else 0
                else:
                    features['time_span_seconds'] = 0
                    features['time_span_minutes'] = 0
                    features['avg_time_interval'] = 0
            except:
                features['time_span_seconds'] = 0
                features['time_span_minutes'] = 0
                features['avg_time_interval'] = 0
        else:
            features['time_span_seconds'] = 0
            features['time_span_minutes'] = 0
            features['avg_time_interval'] = 0
        
        return features
    
    def extract_text_features(self, content: str, max_features: int = 1000) -> np.ndarray:
        """提取文本特征（TF-IDF）"""
        if self.tfidf_vectorizer is None:
            # 自定义预处理：保留5G相关术语
            def custom_preprocessor(text):
                # 保留重要的5G术语和错误信息
                text = re.sub(r'\d{4}-\d{2}-\d{2}[T ]\d{2}:\d{2}:\d{2}[^\s]*', ' TIMESTAMP ', text)
                text = re.sub(r'\b(?:\d{1,3}\.){3}\d{1,3}\b', ' IPADDR ', text)
                text = re.sub(r'[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}', ' UUID ', text)
                return text.lower()
            
            self.tfidf_vectorizer = TfidfVectorizer(
                max_features=max_features,
                ngram_range=(1, 2),
                preprocessor=custom_preprocessor,
                stop_words=None,  # 不使用停用词，保留所有5G术语
                min_df=2,
                max_df=0.8
            )
        
        return self.tfidf_vectorizer
    
    def _get_empty_statistical_features(self) -> Dict:
        """返回空的统计特征"""
        features = {
            'total_lines': 0, 'total_chars': 0, 'avg_line_length': 0,
            'max_line_length': 0, 'min_line_length': 0, 'unique_lines_ratio': 0,
            'error_severity_score': 0
        }
        
        # 日志级别特征
        log_levels = ['trace', 'debug', 'info', 'warn', 'warning', 'error', 'fatal', 'critical']
        for level in log_levels:
            features[f'{level}_count'] = 0
            features[f'{level}_ratio'] = 0
        
        return features
    
    def extract_all_features(self, content: str) -> Dict:
        """提取所有特征"""
        features = {}
        
        # 统计特征
        features.update(self.extract_statistical_features(content))
        
        # 5G网络特征
        features.update(self.extract_5g_network_features(content))
        
        # 故障特征
        features.update(self.extract_fault_features(content))
        
        # 时间特征
        features.update(self.extract_temporal_features(content))
        
        return features
