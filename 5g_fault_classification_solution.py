#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
5G核心网日志故障分类竞赛解决方案
专门针对Macro F1-score优化和类别不平衡问题

竞赛目标: 8分类任务 (0-7)，其中0为正常，1-7为不同故障类型
评价指标: 加权Macro F1-score
"""

import os
import re
import pandas as pd
import numpy as np
from pathlib import Path
from collections import Counter, defaultdict
import warnings
warnings.filterwarnings('ignore')

# 机器学习库
from sklearn.model_selection import StratifiedKFold, cross_val_score, GridSearchCV
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier, VotingClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.svm import SVC
from sklearn.metrics import classification_report, f1_score, confusion_matrix
from sklearn.utils.class_weight import compute_class_weight
from imblearn.over_sampling import SMOTE, ADASYN
from imblearn.combine import SMOTEENN
import xgboost as xgb
import lightgbm as lgb

# 文本处理
import jieba
from datetime import datetime
import json

class G5LogFeatureExtractor:
    """5G核心网日志特征提取器"""
    
    def __init__(self):
        # 5G核心网网元
        self.g5_network_functions = [
            'AMF', 'SMF', 'UPF', 'AUSF', 'UDM', 'UDR', 'PCF', 'NRF', 'NSSF', 'NEF',
            'SEPP', 'SCP', 'BSF', 'CHF', 'NWDAF', 'AF', 'GMLC', 'LMF'
        ]
        
        # 5G接口和协议
        self.g5_interfaces = [
            'N1', 'N2', 'N3', 'N4', 'N6', 'N7', 'N8', 'N9', 'N10', 'N11', 'N12', 'N13', 'N14', 'N15', 'N16', 'N17',
            'NGAP', 'HTTP2', 'SBI', 'PFCP', 'GTP', 'SCTP', 'DIAMETER'
        ]
        
        # 5G业务流程
        self.g5_procedures = [
            'Registration', 'Authentication', 'Authorization', 'Handover', 'PDU Session',
            'Service Request', 'Deregistration', 'Location Update', 'Paging'
        ]
        
        # 故障关键词分类
        self.fault_keywords = {
            'connection': ['connection', 'connect', 'disconnect', 'timeout', 'unreachable'],
            'authentication': ['auth', 'authentication', 'login', 'credential', 'certificate'],
            'resource': ['memory', 'cpu', 'disk', 'resource', 'capacity', 'overload'],
            'protocol': ['protocol', 'parse', 'decode', 'encode', 'format', 'invalid'],
            'service': ['service', 'unavailable', 'down', 'failed', 'error', 'exception']
        }
        
        # 日志级别权重
        self.log_level_weights = {
            'FATAL': 5, 'CRITICAL': 5, 'ERROR': 4, 'WARN': 3, 'WARNING': 3,
            'INFO': 2, 'DEBUG': 1, 'TRACE': 1
        }
        
        # 编译正则表达式
        self._compile_patterns()
    
    def _compile_patterns(self):
        """编译正则表达式模式"""
        self.patterns = {
            'timestamp': re.compile(r'\d{4}-\d{2}-\d{2}[T ]\d{2}:\d{2}:\d{2}'),
            'log_level': re.compile(r'\[(FATAL|CRITICAL|ERROR|WARN|WARNING|INFO|DEBUG|TRACE)\]', re.IGNORECASE),
            'network_function': re.compile(r'\[(' + '|'.join(self.g5_network_functions) + r')\]', re.IGNORECASE),
            'interface': re.compile(r'\b(' + '|'.join(self.g5_interfaces) + r')\b', re.IGNORECASE),
            'procedure': re.compile(r'\b(' + '|'.join(self.g5_procedures) + r')\b', re.IGNORECASE),
            'ip_address': re.compile(r'\b(?:\d{1,3}\.){3}\d{1,3}\b'),
            'hex_id': re.compile(r'\b[0-9a-fA-F]{8,}\b'),
            'number': re.compile(r'\b\d+\b')
        }
    
    def extract_features_from_file(self, file_path: str) -> dict:
        """从单个日志文件提取特征"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
        except UnicodeDecodeError:
            try:
                with open(file_path, 'r', encoding='gbk') as f:
                    content = f.read()
            except:
                with open(file_path, 'r', encoding='latin1') as f:
                    content = f.read()
        
        lines = content.strip().split('\n')
        lines = [line.strip() for line in lines if line.strip()]
        
        if not lines:
            return self._get_empty_features()
        
        features = {}
        
        # 基础统计特征
        features.update(self._extract_basic_stats(lines))
        
        # 时间序列特征
        features.update(self._extract_temporal_features(lines))
        
        # 5G网元特征
        features.update(self._extract_network_function_features(lines))
        
        # 日志级别特征
        features.update(self._extract_log_level_features(lines))
        
        # 故障模式特征
        features.update(self._extract_fault_pattern_features(lines))
        
        # 文本语义特征
        features.update(self._extract_text_features(lines))
        
        # 序列模式特征
        features.update(self._extract_sequence_features(lines))
        
        return features
    
    def _extract_basic_stats(self, lines):
        """提取基础统计特征"""
        total_lines = len(lines)
        total_chars = sum(len(line) for line in lines)
        
        return {
            'total_lines': total_lines,
            'total_chars': total_chars,
            'avg_line_length': total_chars / total_lines if total_lines > 0 else 0,
            'max_line_length': max(len(line) for line in lines) if lines else 0,
            'min_line_length': min(len(line) for line in lines) if lines else 0,
            'unique_lines_ratio': len(set(lines)) / total_lines if total_lines > 0 else 0
        }
    
    def _extract_temporal_features(self, lines):
        """提取时间序列特征"""
        timestamps = []
        for line in lines:
            match = self.patterns['timestamp'].search(line)
            if match:
                try:
                    ts_str = match.group().replace('T', ' ')[:19]
                    ts = datetime.strptime(ts_str, '%Y-%m-%d %H:%M:%S')
                    timestamps.append(ts)
                except:
                    continue
        
        if len(timestamps) < 2:
            return {
                'time_span_seconds': 0,
                'avg_time_interval': 0,
                'time_density': 0,
                'has_time_gaps': 0
            }
        
        timestamps.sort()
        time_span = (timestamps[-1] - timestamps[0]).total_seconds()
        intervals = [(timestamps[i] - timestamps[i-1]).total_seconds() 
                    for i in range(1, len(timestamps))]
        
        return {
            'time_span_seconds': time_span,
            'avg_time_interval': np.mean(intervals) if intervals else 0,
            'time_density': len(timestamps) / (time_span + 1),
            'has_time_gaps': int(any(interval > 60 for interval in intervals))
        }
    
    def _extract_network_function_features(self, lines):
        """提取5G网元特征"""
        nf_counts = Counter()
        interface_counts = Counter()
        procedure_counts = Counter()
        
        for line in lines:
            # 网元统计
            nf_matches = self.patterns['network_function'].findall(line)
            for nf in nf_matches:
                nf_counts[nf.upper()] += 1
            
            # 接口统计
            interface_matches = self.patterns['interface'].findall(line)
            for interface in interface_matches:
                interface_counts[interface.upper()] += 1
            
            # 业务流程统计
            procedure_matches = self.patterns['procedure'].findall(line)
            for proc in procedure_matches:
                procedure_counts[proc.lower()] += 1
        
        features = {}
        
        # 网元特征
        for nf in self.g5_network_functions:
            features[f'nf_{nf.lower()}_count'] = nf_counts.get(nf, 0)
        
        features['total_nf_mentions'] = sum(nf_counts.values())
        features['unique_nf_count'] = len(nf_counts)
        
        # 接口特征
        features['total_interface_mentions'] = sum(interface_counts.values())
        features['unique_interface_count'] = len(interface_counts)
        
        # 业务流程特征
        features['total_procedure_mentions'] = sum(procedure_counts.values())
        features['unique_procedure_count'] = len(procedure_counts)
        
        return features
    
    def _extract_log_level_features(self, lines):
        """提取日志级别特征"""
        level_counts = Counter()
        level_weights_sum = 0
        
        for line in lines:
            match = self.patterns['log_level'].search(line)
            if match:
                level = match.group(1).upper()
                level_counts[level] += 1
                level_weights_sum += self.log_level_weights.get(level, 1)
        
        total_levels = sum(level_counts.values())
        
        features = {
            'error_count': level_counts.get('ERROR', 0) + level_counts.get('FATAL', 0) + level_counts.get('CRITICAL', 0),
            'warning_count': level_counts.get('WARN', 0) + level_counts.get('WARNING', 0),
            'info_count': level_counts.get('INFO', 0),
            'debug_count': level_counts.get('DEBUG', 0) + level_counts.get('TRACE', 0),
            'total_level_mentions': total_levels,
            'avg_log_severity': level_weights_sum / total_levels if total_levels > 0 else 0
        }
        
        # 级别比例
        if total_levels > 0:
            features['error_ratio'] = features['error_count'] / total_levels
            features['warning_ratio'] = features['warning_count'] / total_levels
            features['info_ratio'] = features['info_count'] / total_levels
        else:
            features['error_ratio'] = features['warning_ratio'] = features['info_ratio'] = 0
        
        return features
    
    def _extract_fault_pattern_features(self, lines):
        """提取故障模式特征"""
        fault_scores = defaultdict(int)
        
        content = ' '.join(lines).lower()
        
        for fault_type, keywords in self.fault_keywords.items():
            for keyword in keywords:
                count = content.count(keyword)
                fault_scores[fault_type] += count
        
        # 特殊故障模式
        fault_scores['timeout_pattern'] = content.count('timeout') + content.count('time out')
        fault_scores['failed_pattern'] = content.count('failed') + content.count('failure')
        fault_scores['exception_pattern'] = content.count('exception') + content.count('error')
        fault_scores['unavailable_pattern'] = content.count('unavailable') + content.count('unreachable')
        
        return {f'fault_{k}': v for k, v in fault_scores.items()}
    
    def _extract_text_features(self, lines):
        """提取文本语义特征"""
        content = ' '.join(lines)
        
        # 字符统计
        features = {
            'chinese_char_count': len(re.findall(r'[\u4e00-\u9fff]', content)),
            'english_word_count': len(re.findall(r'\b[a-zA-Z]+\b', content)),
            'number_count': len(self.patterns['number'].findall(content)),
            'ip_address_count': len(self.patterns['ip_address'].findall(content)),
            'hex_id_count': len(self.patterns['hex_id'].findall(content))
        }
        
        # 特殊字符
        features['bracket_count'] = content.count('[') + content.count(']')
        features['colon_count'] = content.count(':')
        features['comma_count'] = content.count(',')
        
        return features
    
    def _extract_sequence_features(self, lines):
        """提取序列模式特征"""
        if len(lines) < 2:
            return {'sequence_complexity': 0, 'pattern_repetition': 0}
        
        # 计算行间相似度
        similarities = []
        for i in range(1, min(len(lines), 10)):  # 只看前10行
            sim = self._calculate_line_similarity(lines[i-1], lines[i])
            similarities.append(sim)
        
        # 检测重复模式
        line_patterns = [self._extract_line_pattern(line) for line in lines[:20]]
        pattern_counts = Counter(line_patterns)
        
        return {
            'sequence_complexity': np.std(similarities) if similarities else 0,
            'pattern_repetition': max(pattern_counts.values()) / len(line_patterns) if line_patterns else 0
        }
    
    def _calculate_line_similarity(self, line1, line2):
        """计算两行日志的相似度"""
        words1 = set(line1.split())
        words2 = set(line2.split())
        if not words1 and not words2:
            return 1.0
        if not words1 or not words2:
            return 0.0
        return len(words1 & words2) / len(words1 | words2)
    
    def _extract_line_pattern(self, line):
        """提取行模式"""
        # 简化行为模式字符串
        pattern = re.sub(r'\d+', 'NUM', line)
        pattern = re.sub(r'\b[0-9a-fA-F]{8,}\b', 'HEX', pattern)
        pattern = re.sub(r'\b(?:\d{1,3}\.){3}\d{1,3}\b', 'IP', pattern)
        return pattern[:50]  # 限制长度
    
    def _get_empty_features(self):
        """返回空特征向量"""
        features = {}
        
        # 基础统计
        for key in ['total_lines', 'total_chars', 'avg_line_length', 'max_line_length', 
                   'min_line_length', 'unique_lines_ratio']:
            features[key] = 0
        
        # 时间特征
        for key in ['time_span_seconds', 'avg_time_interval', 'time_density', 'has_time_gaps']:
            features[key] = 0
        
        # 网元特征
        for nf in self.g5_network_functions:
            features[f'nf_{nf.lower()}_count'] = 0
        for key in ['total_nf_mentions', 'unique_nf_count', 'total_interface_mentions', 
                   'unique_interface_count', 'total_procedure_mentions', 'unique_procedure_count']:
            features[key] = 0
        
        # 日志级别特征
        for key in ['error_count', 'warning_count', 'info_count', 'debug_count', 
                   'total_level_mentions', 'avg_log_severity', 'error_ratio', 'warning_ratio', 'info_ratio']:
            features[key] = 0
        
        # 故障模式特征
        for fault_type in self.fault_keywords.keys():
            features[f'fault_{fault_type}'] = 0
        for key in ['fault_timeout_pattern', 'fault_failed_pattern', 'fault_exception_pattern', 'fault_unavailable_pattern']:
            features[key] = 0
        
        # 文本特征
        for key in ['chinese_char_count', 'english_word_count', 'number_count', 'ip_address_count', 
                   'hex_id_count', 'bracket_count', 'colon_count', 'comma_count']:
            features[key] = 0
        
        # 序列特征
        for key in ['sequence_complexity', 'pattern_repetition']:
            features[key] = 0
        
        return features


class G5FaultClassifier:
    """5G核心网故障分类器"""

    def __init__(self):
        self.feature_extractor = G5LogFeatureExtractor()
        self.scaler = StandardScaler()
        self.tfidf_vectorizer = TfidfVectorizer(
            max_features=1000,
            ngram_range=(1, 2),
            stop_words='english',
            min_df=2,
            max_df=0.8
        )
        self.models = {}
        self.ensemble_model = None
        self.feature_names = []
        self.is_fitted = False

    def load_data(self, train_dir, test_dir=None):
        """加载训练和测试数据"""
        print("🔍 加载数据...")

        # 加载训练数据
        train_path = Path(train_dir)
        train_files = list(train_path.glob("*.txt"))

        if not train_files:
            raise ValueError(f"在 {train_dir} 中没有找到txt文件")

        print(f"找到 {len(train_files)} 个训练文件")

        # 提取训练特征
        X_train_features = []
        X_train_texts = []
        y_train = []

        for file_path in train_files:
            # 从文件名提取标签 (假设格式为 "类别_编号.txt")
            file_name = file_path.stem
            try:
                label = int(file_name.split('_')[0])
                y_train.append(label)
            except:
                # 如果文件名不包含标签，尝试其他方式
                print(f"⚠️  无法从文件名 {file_name} 提取标签，跳过")
                continue

            # 提取特征
            features = self.feature_extractor.extract_features_from_file(str(file_path))
            X_train_features.append(features)

            # 提取文本内容用于TF-IDF
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    text = f.read()
            except:
                try:
                    with open(file_path, 'r', encoding='gbk') as f:
                        text = f.read()
                except:
                    text = ""
            X_train_texts.append(text)

        # 转换为DataFrame
        self.X_train_df = pd.DataFrame(X_train_features)
        self.X_train_texts = X_train_texts
        self.y_train = np.array(y_train)

        print(f"训练数据: {len(self.X_train_df)} 个样本, {len(self.X_train_df.columns)} 个特征")
        print(f"标签分布: {Counter(self.y_train)}")

        # 加载测试数据（如果提供）
        if test_dir:
            test_path = Path(test_dir)
            test_files = list(test_path.glob("*.txt"))

            if test_files:
                print(f"找到 {len(test_files)} 个测试文件")

                X_test_features = []
                X_test_texts = []
                test_file_names = []

                for file_path in sorted(test_files):
                    features = self.feature_extractor.extract_features_from_file(str(file_path))
                    X_test_features.append(features)

                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            text = f.read()
                    except:
                        try:
                            with open(file_path, 'r', encoding='gbk') as f:
                                text = f.read()
                        except:
                            text = ""
                    X_test_texts.append(text)
                    test_file_names.append(file_path.stem)

                self.X_test_df = pd.DataFrame(X_test_features)
                self.X_test_texts = X_test_texts
                self.test_file_names = test_file_names

                print(f"测试数据: {len(self.X_test_df)} 个样本")

    def preprocess_features(self):
        """预处理特征"""
        print("🔧 预处理特征...")

        # 填充缺失值
        self.X_train_df = self.X_train_df.fillna(0)

        # 获取特征名称
        self.feature_names = list(self.X_train_df.columns)

        # 标准化数值特征
        X_train_scaled = self.scaler.fit_transform(self.X_train_df)

        # TF-IDF特征
        X_train_tfidf = self.tfidf_vectorizer.fit_transform(self.X_train_texts)

        # 合并特征
        self.X_train_combined = np.hstack([X_train_scaled, X_train_tfidf.toarray()])

        print(f"合并后特征维度: {self.X_train_combined.shape}")

        # 处理测试数据（如果存在）
        if hasattr(self, 'X_test_df'):
            self.X_test_df = self.X_test_df.fillna(0)

            # 确保测试数据有相同的特征列
            for col in self.feature_names:
                if col not in self.X_test_df.columns:
                    self.X_test_df[col] = 0

            self.X_test_df = self.X_test_df[self.feature_names]

            X_test_scaled = self.scaler.transform(self.X_test_df)
            X_test_tfidf = self.tfidf_vectorizer.transform(self.X_test_texts)
            self.X_test_combined = np.hstack([X_test_scaled, X_test_tfidf.toarray()])

    def handle_class_imbalance(self):
        """处理类别不平衡"""
        print("⚖️  处理类别不平衡...")

        # 分析类别分布
        class_counts = Counter(self.y_train)
        print(f"原始类别分布: {class_counts}")

        # 使用SMOTE进行过采样
        smote = SMOTE(random_state=42, k_neighbors=min(3, min(class_counts.values())-1))

        try:
            self.X_train_balanced, self.y_train_balanced = smote.fit_resample(
                self.X_train_combined, self.y_train
            )

            balanced_counts = Counter(self.y_train_balanced)
            print(f"平衡后类别分布: {balanced_counts}")

        except Exception as e:
            print(f"SMOTE失败: {e}")
            print("使用原始数据...")
            self.X_train_balanced = self.X_train_combined
            self.y_train_balanced = self.y_train

        # 计算类别权重
        self.class_weights = compute_class_weight(
            'balanced',
            classes=np.unique(self.y_train),
            y=self.y_train
        )
        self.class_weight_dict = dict(zip(np.unique(self.y_train), self.class_weights))
        print(f"类别权重: {self.class_weight_dict}")

    def train_models(self):
        """训练多个模型"""
        print("🚀 训练模型...")

        # 定义模型
        models = {
            'rf': RandomForestClassifier(
                n_estimators=200,
                max_depth=15,
                min_samples_split=5,
                min_samples_leaf=2,
                class_weight=self.class_weight_dict,
                random_state=42,
                n_jobs=-1
            ),
            'xgb': xgb.XGBClassifier(
                n_estimators=200,
                max_depth=8,
                learning_rate=0.1,
                subsample=0.8,
                colsample_bytree=0.8,
                random_state=42,
                eval_metric='mlogloss'
            ),
            'lgb': lgb.LGBMClassifier(
                n_estimators=200,
                max_depth=8,
                learning_rate=0.1,
                subsample=0.8,
                colsample_bytree=0.8,
                class_weight=self.class_weight_dict,
                random_state=42,
                verbose=-1
            ),
            'lr': LogisticRegression(
                class_weight=self.class_weight_dict,
                random_state=42,
                max_iter=1000,
                multi_class='ovr'
            )
        }

        # 训练每个模型并评估
        cv_scores = {}

        for name, model in models.items():
            print(f"\n训练 {name.upper()} 模型...")

            # 交叉验证
            cv_score = cross_val_score(
                model, self.X_train_balanced, self.y_train_balanced,
                cv=StratifiedKFold(n_splits=5, shuffle=True, random_state=42),
                scoring='f1_macro',
                n_jobs=-1
            )

            cv_scores[name] = cv_score
            print(f"{name.upper()} CV Macro F1: {cv_score.mean():.4f} (+/- {cv_score.std() * 2:.4f})")

            # 训练完整模型
            model.fit(self.X_train_balanced, self.y_train_balanced)
            self.models[name] = model

        # 创建集成模型
        print("\n🎯 创建集成模型...")

        # 选择表现最好的几个模型
        best_models = sorted(cv_scores.items(), key=lambda x: x[1].mean(), reverse=True)[:3]
        print(f"选择的模型: {[name for name, _ in best_models]}")

        voting_models = [(name, self.models[name]) for name, _ in best_models]

        self.ensemble_model = VotingClassifier(
            estimators=voting_models,
            voting='soft'
        )

        self.ensemble_model.fit(self.X_train_balanced, self.y_train_balanced)

        # 集成模型交叉验证
        ensemble_cv_score = cross_val_score(
            self.ensemble_model, self.X_train_balanced, self.y_train_balanced,
            cv=StratifiedKFold(n_splits=5, shuffle=True, random_state=42),
            scoring='f1_macro',
            n_jobs=-1
        )

        print(f"集成模型 CV Macro F1: {ensemble_cv_score.mean():.4f} (+/- {ensemble_cv_score.std() * 2:.4f})")

        self.is_fitted = True

    def predict(self, use_ensemble=True):
        """预测测试数据"""
        if not self.is_fitted:
            raise ValueError("模型尚未训练")

        if not hasattr(self, 'X_test_combined'):
            raise ValueError("没有测试数据")

        print("🔮 预测测试数据...")

        if use_ensemble:
            predictions = self.ensemble_model.predict(self.X_test_combined)
            probabilities = self.ensemble_model.predict_proba(self.X_test_combined)
        else:
            # 使用最佳单模型
            best_model_name = max(self.models.keys(),
                                key=lambda x: cross_val_score(
                                    self.models[x], self.X_train_balanced, self.y_train_balanced,
                                    cv=3, scoring='f1_macro'
                                ).mean())
            predictions = self.models[best_model_name].predict(self.X_test_combined)
            probabilities = self.models[best_model_name].predict_proba(self.X_test_combined)

        return predictions, probabilities

    def save_results(self, predictions, output_file='result.csv'):
        """保存预测结果"""
        print(f"💾 保存结果到 {output_file}...")

        if not hasattr(self, 'test_file_names'):
            # 如果没有文件名，使用序号
            file_indices = list(range(1, len(predictions) + 1))
        else:
            # 从文件名提取序号
            file_indices = []
            for name in self.test_file_names:
                try:
                    # 假设文件名格式为 "数字.txt" 或包含数字
                    idx = int(re.search(r'\d+', name).group())
                    file_indices.append(idx)
                except:
                    file_indices.append(len(file_indices) + 1)

        # 创建结果DataFrame
        results_df = pd.DataFrame({
            '日志片段文件编号': file_indices,
            '故障类型': predictions
        })

        # 按文件编号排序
        results_df = results_df.sort_values('日志片段文件编号')

        # 保存为GB2312编码
        results_df.to_csv(output_file, index=False, encoding='gb2312')

        print(f"✅ 结果已保存: {len(results_df)} 个预测")
        print(f"预测分布: {Counter(predictions)}")

    def evaluate_on_validation(self):
        """在验证集上评估模型"""
        print("📊 模型评估...")

        # 使用部分训练数据作为验证集
        from sklearn.model_selection import train_test_split

        X_train_split, X_val_split, y_train_split, y_val_split = train_test_split(
            self.X_train_combined, self.y_train,
            test_size=0.2, random_state=42, stratify=self.y_train
        )

        # 在分割的训练集上重新训练
        temp_ensemble = VotingClassifier(
            estimators=[(name, model) for name, model in self.models.items()],
            voting='soft'
        )
        temp_ensemble.fit(X_train_split, y_train_split)

        # 预测验证集
        y_pred = temp_ensemble.predict(X_val_split)

        # 计算指标
        f1_macro = f1_score(y_val_split, y_pred, average='macro')
        f1_weighted = f1_score(y_val_split, y_pred, average='weighted')

        print(f"验证集 Macro F1: {f1_macro:.4f}")
        print(f"验证集 Weighted F1: {f1_weighted:.4f}")

        # 详细分类报告
        print("\n分类报告:")
        print(classification_report(y_val_split, y_pred))

        return f1_macro, f1_weighted


def main():
    """主函数"""
    print("🚀 5G核心网日志故障分类竞赛解决方案")
    print("="*60)

    # 配置路径
    TRAIN_DIR = "files/train"  # 训练数据目录
    TEST_DIR = "files/test"    # 测试数据目录
    OUTPUT_FILE = "result.csv" # 输出文件

    try:
        # 创建分类器
        classifier = G5FaultClassifier()

        # 加载数据
        classifier.load_data(TRAIN_DIR, TEST_DIR)

        # 预处理特征
        classifier.preprocess_features()

        # 处理类别不平衡
        classifier.handle_class_imbalance()

        # 训练模型
        classifier.train_models()

        # 评估模型
        classifier.evaluate_on_validation()

        # 预测测试数据
        predictions, probabilities = classifier.predict(use_ensemble=True)

        # 保存结果
        classifier.save_results(predictions, OUTPUT_FILE)

        print("\n🎉 任务完成!")
        print(f"预测结果已保存到: {OUTPUT_FILE}")

    except Exception as e:
        print(f"❌ 发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
