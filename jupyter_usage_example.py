"""
Jupyter Notebook中使用5G核心网日志清洗工具的示例

复制以下代码到Jupyter Notebook的cell中运行
"""

# =============================================================================
# 示例1: 基础使用
# =============================================================================

# 导入工具
from jupyter_5g_cleaner import OptimizedLogCleaner

# 创建清洗器实例
cleaner = OptimizedLogCleaner()

# 处理文件
results = cleaner.process_files(
    input_dir="/path/to/your/188files",  # 替换为您的输入目录
    output_dir="/path/to/cleaned_output"  # 替换为您的输出目录
)

# 查看结果
print(f"处理结果: {results}")

# =============================================================================
# 示例2: 处理特定模式的文件
# =============================================================================

# 只处理特定模式的文件
results = cleaner.process_files(
    input_dir="/path/to/logs",
    output_dir="/path/to/cleaned",
    file_pattern="log_*.txt"  # 只处理以log_开头的txt文件
)

# =============================================================================
# 示例3: 处理单个文件（测试用）
# =============================================================================

# 测试单个文件
test_result = cleaner.process_single_file_for_testing(
    input_file="/path/to/test_file.txt",
    output_file="/path/to/cleaned_test_file.txt"
)

print(f"单文件测试结果: {test_result}")

# =============================================================================
# 示例4: 重置统计信息后重新处理
# =============================================================================

# 重置统计信息
cleaner.reset_stats()

# 重新处理另一批文件
results2 = cleaner.process_files(
    input_dir="/path/to/another/batch",
    output_dir="/path/to/another/output"
)

# =============================================================================
# 示例5: 检查内存使用情况
# =============================================================================

# 检查当前内存使用
current_memory = cleaner.memory_monitor.get_memory_gb()
peak_memory = cleaner.memory_monitor.peak_memory

print(f"当前内存使用: {current_memory:.2f}GB")
print(f"峰值内存使用: {peak_memory:.2f}GB")

# =============================================================================
# 示例6: 错误处理
# =============================================================================

try:
    results = cleaner.process_files(
        input_dir="/nonexistent/path",
        output_dir="/output/path"
    )
    
    if "error" in results:
        print(f"处理出错: {results['error']}")
    else:
        print("处理成功!")
        
except Exception as e:
    print(f"发生异常: {e}")

# =============================================================================
# 预期输出示例
# =============================================================================

"""
运行后的预期输出:

开始处理 188 个文件...
进度: 20/188 (10.6%) | 内存: 2.1GB
进度: 40/188 (21.3%) | 内存: 2.8GB
进度: 60/188 (31.9%) | 内存: 3.2GB
...
进度: 188/188 (100.0%) | 内存: 2.5GB

处理完成!
成功: 185/188 (98.4%)
数据: 28,500,000 → 8,550,000 行 (30.0%)
用时: 45.2 分钟 | 峰值内存: 4.2GB

处理结果: {
    'total_files': 188,
    'processed_files': 185,
    'failed_files': 3,
    'success_rate': 98.4,
    'total_lines_processed': 28500000,
    'total_lines_kept': 8550000,
    'line_retention_rate': 30.0,
    'processing_time_minutes': 45.2,
    'peak_memory_gb': 4.2,
    'files_per_minute': 4.1,
    'failed_files_count': 3,
    'failed_files_list': ['file_023.txt: 编码错误', 'file_156.txt: 文件损坏']
}
"""
