# 5G核心网日志大规模处理指南

## 🎯 专为您的数据集设计

本工具专门针对您的**188个大规模5G核心网日志文件**进行了优化，每个文件包含10万-20万行日志数据，总计约1,880万-3,760万行日志。

## 🚀 快速开始

### 1. 环境准备

```bash
# 安装依赖
pip install tqdm psutil pandas numpy

# 确保有足够的磁盘空间（建议至少是原数据的1.5倍）
# 确保有足够的内存（建议至少8GB）
```

### 2. 基础使用

```bash
# 处理您的188个文件
python process_5g_logs.py --input-dir /path/to/your/188files --output-dir /path/to/cleaned --workers 4

# 高性能处理（推荐）
python process_5g_logs.py --input-dir /path/to/your/188files --output-dir /path/to/cleaned --workers 8
```

### 3. 进度显示效果

```
🔥 5G核心网日志处理工具 - 大规模数据处理版
================================================================================

📁 数据集信息:
   📄 文件数量: 188 个
   💾 总数据量: 15.2 GB
   🔧 并行线程: 8 个

📊 文件大小分布:
   📏 平均大小: 82.3 MB
   📏 最大文件: 156.7 MB
   📏 最小文件: 45.2 MB

⏱️  预估处理时间: 2.1小时

🚀 即将开始处理...
================================================================================

✅ file_001.txt | 内存: 2.3GB | ETA: 1.8小时 |  45%|████▌     | 85/188 [23:45<28:12, 16.4s/文件]
```

## 📊 详细功能特性

### 🎛️ 多层级进度显示

1. **主进度条**：显示整体文件处理进度
   - 当前处理的文件名
   - 已处理/总文件数
   - 处理速度（文件/分钟）
   - 预估剩余时间

2. **文件级进度**：大文件（>50MB）显示读取进度
   - 文件读取进度条
   - 行分析进度条

3. **实时监控**：
   - 内存使用情况
   - 处理速度统计
   - 错误文件实时提醒

### 🧠 智能处理策略

#### 针对您的大规模数据优化：

1. **内存友好处理**
   ```python
   # 大文件使用内存映射
   # 智能行采样（保留关键信息）
   # 实时垃圾回收
   ```

2. **5G专业特征保留**
   ```python
   # 优先保留：错误日志、5G网元交互、故障信息
   # 智能采样：普通日志按重要性采样
   # 上下文保留：错误日志前后2行上下文
   ```

3. **并行处理优化**
   ```python
   # 多线程并行处理
   # 进度同步显示
   # 内存使用监控
   ```

## 📈 性能预期

### 基于您的数据规模（188文件，~20GB数据）：

| 配置 | 预估处理时间 | 内存使用 | 推荐场景 |
|------|-------------|----------|----------|
| 2线程 | 4-6小时 | 2-4GB | 内存受限环境 |
| 4线程 | 2-3小时 | 4-6GB | 标准配置 |
| 8线程 | 1-2小时 | 6-8GB | 高性能处理 |

### 数据压缩效果：
- **行数保留率**：60-80%（保留关键信息）
- **文件大小压缩**：40-60%（移除噪声数据）
- **处理速度**：约10-20万行/秒

## 🛠️ 高级使用

### 1. 断点续传
```bash
# 如果处理中断，可以续传
python process_5g_logs.py --input-dir ./logs --output-dir ./cleaned --resume
```

### 2. 仅分析模式
```bash
# 先分析数据集，不执行实际处理
python process_5g_logs.py --input-dir ./logs --output-dir ./cleaned --dry-run
```

### 3. 自定义配置
```bash
# 内存受限环境
python process_5g_logs.py --input-dir ./logs --output-dir ./cleaned --workers 2

# 高性能环境
python process_5g_logs.py --input-dir ./logs --output-dir ./cleaned --workers 12
```

## 📋 处理报告

处理完成后会生成详细报告：

### 1. JSON格式报告 (`processing_report.json`)
```json
{
  "processing_summary": {
    "total_files": 188,
    "processed_files": 185,
    "failed_files": 2,
    "empty_files": 1,
    "success_rate": 98.4
  },
  "data_statistics": {
    "total_lines_processed": 28500000,
    "total_lines_kept": 19800000,
    "line_retention_rate": 69.5,
    "compression_ratio": 45.2
  },
  "performance_metrics": {
    "total_processing_time_formatted": "1:23:45",
    "average_speed_files_per_minute": 2.24,
    "average_lines_per_second": 5680
  }
}
```

### 2. 文本摘要报告 (`processing_summary.txt`)
```
================================================================================
5G核心网日志处理报告
================================================================================

📊 处理摘要:
  总文件数: 188
  成功处理: 185 (98.4%)
  处理失败: 2
  空文件: 1

📈 数据统计:
  处理行数: 28,500,000
  保留行数: 19,800,000
  行保留率: 69.5%
  原始大小: 18,234.5 MB
  清洗后大小: 9,987.2 MB
  压缩率: 45.2%

⚡ 性能指标:
  总处理时间: 1:23:45
  处理速度: 2.2 文件/分钟
  行处理速度: 5,680 行/秒
```

## 🔧 故障排除

### 常见问题：

1. **内存不足**
   ```bash
   # 减少并行线程数
   python process_5g_logs.py --workers 2
   ```

2. **处理速度慢**
   ```bash
   # 增加并行线程数（确保有足够内存）
   python process_5g_logs.py --workers 8
   ```

3. **部分文件失败**
   ```bash
   # 查看详细错误日志
   cat 5g_log_processing.log
   
   # 使用断点续传重试
   python process_5g_logs.py --resume
   ```

## 💡 最佳实践

### 针对您的188个文件：

1. **推荐配置**：
   - 线程数：4-8个
   - 内存：至少8GB
   - 磁盘空间：至少30GB可用

2. **处理策略**：
   - 先用`--dry-run`分析数据
   - 使用`--workers 4`开始处理
   - 如果内存充足，可增加到8个线程

3. **监控要点**：
   - 关注内存使用情况
   - 记录失败文件信息
   - 保存处理报告用于后续分析

## 🎯 预期结果

处理完成后，您将获得：

- ✅ **185-188个清洗后的日志文件**
- ✅ **约60-80%的关键日志行保留**
- ✅ **40-60%的存储空间节省**
- ✅ **完整的处理报告和统计信息**
- ✅ **优化后的5G故障分类特征数据**

这些清洗后的数据将更适合用于5G核心网故障分类任务，提高模型训练效率和准确性。
