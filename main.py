# ============================================================================
# 5G核心网日志数据清洗工具 - 内存友好版本
# ============================================================================

import os
import re
import json
import pandas as pd
import numpy as np
from collections import Counter, defaultdict
from datetime import datetime
import pickle
import gc
import time
from tqdm import tqdm


class MemoryFriendlyLogCleaner:
    def __init__(self, train_dir='files/train', test_dir='files/test'):
        self.train_dir = train_dir
        self.test_dir = test_dir
        self.cleaning_stats = {
            'original_files': 0,
            'cleaned_files': 0,
            'empty_files': 0,
            'corrupted_files': 0,
            'oversized_files': 0,
            'total_size_before': 0,
            'total_size_after': 0,
            'lines_processed': 0,
            'lines_kept': 0,
        }

        # 5G核心网关键模式（更全面）
        self.g5_patterns = [
            r'\b(AMF|SMF|UPF|AUSF|UDM|UDR|PCF|NRF|NSSF|NEF)\b',
            r'\b(SEPP|SCP|BSF|CHF|NWDAF|AF|GMLC|LMF)\b',
            r'\b(NGAP|HTTP|SBI|PDU|Session|SUPI|PLMN|DNN|QoS|Slice)\b',
            r'\b(Registration|Authentication|Authorization|Handover)\b'
        ]

        # 重要日志标识
        self.important_patterns = [
            r'\[(ERROR|FATAL|CRITICAL)\]',
            r'\[(WARN|WARNING)\]',
            r'(failed|failure|error|timeout|exception|denied|reject|abort)',
            r'(success|complete|established|registered)',
            r'(AMF|SMF|UPF).*?(procedure|request|response)'
        ]

        # 噪声模式（需要清理的）
        self.noise_patterns = [
            r'^\s*$',  # 空行或只有空白的行
            r'^[\s\-=\*#]+$',  # 只有符号的行
            r'(.)\1{100,}',  # 超过100个重复字符
            r'[\x00-\x08\x0b\x0c\x0e-\x1f\x7f-\xff]',  # 控制字符
        ]

    def analyze_raw_data(self):
        """分析原始数据的特征和问题"""
        print("=== 开始分析原始日志数据 ===")

        analysis_report = {
            'file_sizes': [],
            'line_counts': [],
            'encoding_issues': [],
            'timestamp_formats': Counter(),
            'log_level_distribution': Counter(),
            'component_mentions': Counter(),
            'problematic_files': []
        }

        # 分析训练数据
        label_file = os.path.join(self.train_dir, 'label.csv')
        if os.path.exists(label_file):
            labels_df = pd.read_csv(label_file)
            print(f"找到标签文件，包含 {len(labels_df)} 条记录")

            for _, row in labels_df.iterrows():
                file_id = int(row['日志片段文件编号'])
                fault_type = row['故障类型']
                file_path = os.path.join(self.train_dir, f'{file_id}.txt')

                if os.path.exists(file_path):
                    self._analyze_single_file(file_path, file_id, analysis_report, fault_type)

        # 分析测试数据
        if os.path.exists(self.test_dir):
            test_files = [f for f in os.listdir(self.test_dir) if f.endswith('.txt')]
            for file_name in test_files:
                file_id = int(file_name.split('.')[0])
                file_path = os.path.join(self.test_dir, file_name)
                self._analyze_single_file(file_path, file_id, analysis_report)

        self._print_analysis_report(analysis_report)
        return analysis_report

    def _analyze_single_file(self, file_path, file_id, report, fault_type=None):
        """分析单个文件"""
        try:
            file_size = os.path.getsize(file_path)
            report['file_sizes'].append(file_size)

            # 尝试不同编码读取
            content = None
            encodings = ['utf-8', 'gbk', 'gb2312', 'latin1']

            for encoding in encodings:
                try:
                    with open(file_path, 'r', encoding=encoding) as f:
                        content = f.read()
                    break
                except UnicodeDecodeError:
                    continue

            if content is None:
                report['encoding_issues'].append(file_id)
                report['problematic_files'].append(f"File {file_id}: 编码问题")
                return

            # 统计行数
            lines = content.split('\n')
            report['line_counts'].append(len(lines))

            # 检测时间戳格式
            timestamp_patterns = [
                r'\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}',
                r'\d{2}/\d{2}/\d{4} \d{2}:\d{2}:\d{2}',
                r'\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}'
            ]
            for pattern in timestamp_patterns:
                if re.search(pattern, content):
                    report['timestamp_formats'][pattern] += 1

            # 检测日志级别
            content_upper = content.upper()
            log_levels = ['ERROR', 'WARN', 'WARNING', 'INFO', 'DEBUG', 'TRACE', 'FATAL']
            for level in log_levels:
                count = content_upper.count(f'[{level}]') + content_upper.count(f'{level}:')
                if count > 0:
                    report['log_level_distribution'][level] += count

            # 检测5G组件
            g5_components = ['AMF', 'SMF', 'UPF', 'AUSF', 'UDM', 'UDR', 'PCF', 'NRF', 'NSSF', 'NEF']
            for component in g5_components:
                count = content_upper.count(component)
                if count > 0:
                    report['component_mentions'][component] += count

            # 检测问题
            if file_size > 10 * 1024 * 1024:  # 超过10MB
                report['problematic_files'].append(f"File {file_id}: 文件过大 ({file_size / 1024 / 1024:.1f}MB)")

            if len(lines) > 50000:  # 超过5万行
                report['problematic_files'].append(f"File {file_id}: 行数过多 ({len(lines)} 行)")

            # 检测异常字符
            if re.search(r'[\x00-\x08\x0b\x0c\x0e-\x1f\x7f-\xff]', content):
                report['problematic_files'].append(f"File {file_id}: 包含异常字符")

        except Exception as e:
            report['problematic_files'].append(f"File {file_id}: 读取失败 - {str(e)}")

    def _print_analysis_report(self, report):
        """打印分析报告"""
        print("\n=== 数据分析报告 ===")

        if report['file_sizes']:
            print(f"文件数量: {len(report['file_sizes'])}")
            print(f"文件大小 - 平均: {np.mean(report['file_sizes']) / 1024:.1f}KB, "
                  f"最大: {max(report['file_sizes']) / 1024:.1f}KB, "
                  f"最小: {min(report['file_sizes']) / 1024:.1f}KB")

        if report['line_counts']:
            print(f"行数统计 - 平均: {np.mean(report['line_counts']):.1f}, "
                  f"最大: {max(report['line_counts'])}, "
                  f"最小: {min(report['line_counts'])}")

        print(f"编码问题文件: {len(report['encoding_issues'])} 个")
        print(f"问题文件: {len(report['problematic_files'])} 个")

        print("\n时间戳格式分布:")
        for pattern, count in report['timestamp_formats'].most_common(3):
            print(f"  {pattern}: {count} 个文件")

        print("\n日志级别分布:")
        for level, count in report['log_level_distribution'].most_common():
            print(f"  {level}: {count} 次出现")

        print("\n5G组件提及频率:")
        for component, count in report['component_mentions'].most_common(10):
            print(f"  {component}: {count} 次")

        if report['problematic_files']:
            print(f"\n问题文件详情:")
            for issue in report['problematic_files'][:10]:  # 只显示前10个
                print(f"  {issue}")
            if len(report['problematic_files']) > 10:
                print(f"  ... 还有 {len(report['problematic_files']) - 10} 个问题文件")

    def clean_single_log(self, content, max_lines=5000, max_chars=500000):
        """清洗单个日志内容"""
        if not content or not content.strip():
            return ""

        original_size = len(content)
        self.cleaning_stats['total_size_before'] += original_size

        # 1. 移除异常字符
        for pattern in self.noise_patterns:
            content = re.sub(pattern, '', content)

        # 2. 标准化换行符
        content = re.sub(r'\r\n|\r', '\n', content)

        # 3. 限制行数（保留前面的重要日志）
        lines = content.split('\n')
        if len(lines) > max_lines:
            # 保留开头和结尾的日志，中间的进行采样
            start_lines = lines[:max_lines // 2]
            end_lines = lines[-max_lines // 4:]
            middle_lines = lines[max_lines // 2:-max_lines // 4]

            # 从中间部分采样，优先保留错误日志
            if middle_lines:
                error_lines = [line for line in middle_lines
                               if any(level in line.upper() for level in ['ERROR', 'WARN', 'FATAL'])]
                other_lines = [line for line in middle_lines
                               if not any(level in line.upper() for level in ['ERROR', 'WARN', 'FATAL'])]

                # 保留所有错误日志 + 采样其他日志
                sample_size = max_lines // 4 - len(error_lines)
                if sample_size > 0 and other_lines:
                    step = max(1, len(other_lines) // sample_size)
                    sampled_other = other_lines[::step][:sample_size]
                else:
                    sampled_other = []

                selected_middle = error_lines + sampled_other
            else:
                selected_middle = []

            lines = start_lines + selected_middle + end_lines

        # 4. 移除空行和只有空白字符的行
        lines = [line.strip() for line in lines if line.strip()]

        # 5. 限制总字符数
        content = '\n'.join(lines)
        if len(content) > max_chars:
            content = content[:max_chars]
            # 确保不在行中间截断
            last_newline = content.rfind('\n')
            if last_newline > 0:
                content = content[:last_newline]

        # 6. 标准化时间戳格式（统一为ISO格式）
        content = self._normalize_timestamps(content)

        # 7. 标准化日志级别格式
        content = self._normalize_log_levels(content)

        self.cleaning_stats['total_size_after'] += len(content)

        return content

    def _normalize_timestamps(self, content):
        """标准化时间戳格式"""
        # 将各种时间格式统一为 YYYY-MM-DD HH:MM:SS 格式
        patterns_replacements = [
            (r'(\d{4})-(\d{2})-(\d{2})T(\d{2}):(\d{2}):(\d{2})(?:\.\d+)?(?:Z|[+-]\d{2}:?\d{2})?',
             r'\1-\2-\3 \4:\5:\6'),
            (r'(\d{2})/(\d{2})/(\d{4}) (\d{2}):(\d{2}):(\d{2})',
             r'\3-\1-\2 \4:\5:\6'),
        ]

        for pattern, replacement in patterns_replacements:
            content = re.sub(pattern, replacement, content)

        return content

    def _normalize_log_levels(self, content):
        """标准化日志级别格式"""
        # 将各种日志级别格式统一为 [LEVEL] 格式
        level_patterns = [
            (r'\b(TRACE|DEBUG|INFO|WARN|WARNING|ERROR|FATAL)\s*:', r'[\1]'),
            (r'\b(TRACE|DEBUG|INFO|WARN|WARNING|ERROR|FATAL)\s*-', r'[\1]'),
            (r'<(TRACE|DEBUG|INFO|WARN|WARNING|ERROR|FATAL)>', r'[\1]'),
        ]

        for pattern, replacement in level_patterns:
            content = re.sub(pattern, replacement, content, flags=re.IGNORECASE)

        return content

    def clean_all_data(self, output_dir='files_cleaned'):
        """清洗所有数据并保存"""
        print("=== 开始清洗所有日志数据 ===")

        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        os.makedirs(os.path.join(output_dir, 'train'), exist_ok=True)
        os.makedirs(os.path.join(output_dir, 'test'), exist_ok=True)

        # 清洗训练数据
        print("清洗训练数据...")
        self._clean_dataset(self.train_dir, os.path.join(output_dir, 'train'), is_train=True)

        # 清洗测试数据
        print("清洗测试数据...")
        self._clean_dataset(self.test_dir, os.path.join(output_dir, 'test'), is_train=False)

        # 保存清洗统计信息
        stats_file = os.path.join(output_dir, 'cleaning_stats.json')
        with open(stats_file, 'w', encoding='utf-8') as f:
            # 转换Counter对象为普通字典以便JSON序列化
            serializable_stats = {}
            for key, value in self.cleaning_stats.items():
                if isinstance(value, Counter):
                    serializable_stats[key] = dict(value)
                else:
                    serializable_stats[key] = value
            json.dump(serializable_stats, f, indent=2, ensure_ascii=False)

        self._print_cleaning_summary(output_dir)

        return output_dir

    def _clean_dataset(self, input_dir, output_dir, is_train=True):
        """清洗数据集"""
        if is_train:
            # 处理训练数据（有标签文件）
            label_file = os.path.join(input_dir, 'label.csv')
            if os.path.exists(label_file):
                labels_df = pd.read_csv(label_file)
                # 复制标签文件
                labels_df.to_csv(os.path.join(output_dir, 'label.csv'), index=False)

                for _, row in labels_df.iterrows():
                    file_id = int(row['日志片段文件编号'])
                    input_file = os.path.join(input_dir, f'{file_id}.txt')
                    output_file = os.path.join(output_dir, f'{file_id}.txt')

                    self._clean_and_save_file(input_file, output_file, file_id)
        else:
            # 处理测试数据
            if os.path.exists(input_dir):
                test_files = [f for f in os.listdir(input_dir) if f.endswith('.txt')]
                for file_name in test_files:
                    file_id = int(file_name.split('.')[0])
                    input_file = os.path.join(input_dir, file_name)
                    output_file = os.path.join(output_dir, file_name)

                    self._clean_and_save_file(input_file, output_file, file_id)

    def _clean_and_save_file(self, input_file, output_file, file_id):
        """清洗并保存单个文件"""
        if not os.path.exists(input_file):
            self.cleaning_stats['corrupted_files'] += 1
            return

        self.cleaning_stats['original_files'] += 1

        # 尝试多种编码读取
        content = None
        encodings = ['utf-8', 'gbk', 'gb2312', 'latin1']

        for encoding in encodings:
            try:
                with open(input_file, 'r', encoding=encoding) as f:
                    content = f.read()
                break
            except UnicodeDecodeError:
                continue

        if content is None:
            print(f"警告: 无法读取文件 {file_id}")
            self.cleaning_stats['corrupted_files'] += 1
            return

        # 检查是否为空文件
        if not content.strip():
            self.cleaning_stats['empty_files'] += 1
            return

        # 检查是否过大
        if len(content) > 10 * 1024 * 1024:  # 10MB
            print(f"警告: 文件 {file_id} 过大 ({len(content) / 1024 / 1024:.1f}MB)，将进行压缩")
            self.cleaning_stats['oversized_files'] += 1

        # 清洗内容
        cleaned_content = self.clean_single_log(content)

        if cleaned_content.strip():
            # 保存清洗后的文件
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(cleaned_content)
            self.cleaning_stats['cleaned_files'] += 1
        else:
            self.cleaning_stats['empty_files'] += 1

    def _print_cleaning_summary(self, output_dir):
        """打印清洗总结"""
        stats = self.cleaning_stats

        print(f"\n=== 数据清洗完成 ===")
        print(f"输出目录: {output_dir}")
        print(f"原始文件: {stats['original_files']} 个")
        print(f"成功清洗: {stats['cleaned_files']} 个")
        print(f"空文件: {stats['empty_files']} 个")
        print(f"损坏文件: {stats['corrupted_files']} 个")
        print(f"过大文件: {stats['oversized_files']} 个")

        if stats['total_size_before'] > 0:
            compression_ratio = (1 - stats['total_size_after'] / stats['total_size_before']) * 100
            print(f"存储空间节省: {compression_ratio:.1f}%")
            print(f"清洗前总大小: {stats['total_size_before'] / 1024 / 1024:.1f}MB")
            print(f"清洗后总大小: {stats['total_size_after'] / 1024 / 1024:.1f}MB")


def create_cleaned_feature_extractor():
    """为清洗后的数据创建特征提取器"""

    class CleanedLogFeatureExtractor:
        def __init__(self):
            self.scaler = None

        def extract_features(self, log_contents):
            """为清洗后的日志提取特征"""
            features_list = []

            for content in log_contents:
                features = self._extract_single_features(content)
                features_list.append(features)

            return np.array(features_list)

        def _extract_single_features(self, content):
            """提取单个日志的特征"""
            if not content:
                return [0] * 20

            lines = content.split('\n')
            non_empty_lines = [line for line in lines if line.strip()]

            features = []

            # 基础统计特征
            features.extend([
                len(non_empty_lines),  # 行数
                len(content),  # 字符数
                np.mean([len(line) for line in non_empty_lines]) if non_empty_lines else 0,  # 平均行长
                max([len(line) for line in non_empty_lines]) if non_empty_lines else 0,  # 最大行长
                min([len(line) for line in non_empty_lines]) if non_empty_lines else 0,  # 最小行长
            ])

            # 日志级别统计
            content_upper = content.upper()
            total_lines = max(len(non_empty_lines), 1)

            features.extend([
                content_upper.count('[ERROR]') / total_lines,
                content_upper.count('[INFO]') / total_lines,
                content_upper.count('[WARN]') / total_lines,
                content_upper.count('[DEBUG]') / total_lines,
            ])

            # 5G组件统计
            components = ['AMF', 'SMF', 'UPF', 'AUSF', 'UDM']
            for component in components:
                features.append(content_upper.count(component) / total_lines)

            # 关键词统计
            keywords = ['FAILED', 'TIMEOUT', 'EXCEPTION', 'SUCCESS', 'COMPLETE']
            for keyword in keywords:
                features.append(content_upper.count(keyword) / total_lines)

            return features

    return CleanedLogFeatureExtractor()


# ============================================================================
# 主执行函数
# ============================================================================

def main_data_cleaning():
    """主数据清洗流程"""
    print("=" * 60)
    print("5G核心网日志数据清洗工具")
    print("=" * 60)

    # 初始化清洗器
    cleaner = MemoryFriendlyLogCleaner()

    # 1. 分析原始数据
    print("\n步骤1: 分析原始数据...")
    analysis_report = cleaner.analyze_raw_data()

    # 2. 询问是否继续清洗
    print(f"\n发现 {len(analysis_report['problematic_files'])} 个问题文件")
    choice = input("是否继续进行数据清洗？(y/n): ").strip().lower()

    if choice != 'y':
        print("数据清洗已取消")
        return

    # 3. 执行清洗
    print("\n步骤2: 执行数据清洗...")
    output_dir = cleaner.clean_all_data()

    print(f"\n数据清洗完成！")
    print(f"清洗后的数据保存在: {output_dir}")
    print(f"现在可以使用清洗后的数据进行模型训练")

    return output_dir


if __name__ == "__main__":
    main_data_cleaning()
