# 按装订区域中的绿色按钮以运行脚本。
import os
from transformers import AutoModelForCausalLM, AutoTokenizer

model_name = "Qwen/Qwen2.5-7B-Instruct"
local_dir  = os.path.join(os.getcwd(), "model")   # 当前项目下的 model 目录

tokenizer = AutoTokenizer.from_pretrained(
                model_name,
                cache_dir=local_dir)

model = AutoModelForCausalLM.from_pretrained(
                model_name,
                cache_dir=local_dir,
                torch_dtype="auto",




python qwen_stable_training.py --gpu 0 --batch_size 1 --max_length 256 --gradient_accumulation 32 --sample_ratio 1.0