#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
5G核心网日志清洗工具 - 进度增强版本
专为大规模数据集处理优化，支持详细进度显示

特性：
1. 多层级进度条显示
2. 实时内存监控
3. 处理速度统计
4. 详细错误报告
5. 并行处理进度同步
"""

import os
import sys
import time
import psutil
import threading
from pathlib import Path
from typing import List, Dict, Tuple, Optional, Union
from dataclasses import dataclass, field
from concurrent.futures import ThreadPoolExecutor, as_completed
from collections import defaultdict, deque
from datetime import datetime, timedelta
import json
import logging

# 进度条相关
from tqdm import tqdm
from tqdm.contrib.concurrent import thread_map

# 基础模块
import re
import pandas as pd
import numpy as np

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('log_processing.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class ProcessingStats:
    """处理统计信息"""
    total_files: int = 0
    processed_files: int = 0
    failed_files: int = 0
    empty_files: int = 0
    total_lines_processed: int = 0
    total_lines_kept: int = 0
    total_size_before: int = 0
    total_size_after: int = 0
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    failed_file_details: List[Dict] = field(default_factory=list)
    processing_speeds: deque = field(default_factory=lambda: deque(maxlen=10))

    def add_failed_file(self, file_path: str, error: str):
        """添加失败文件记录"""
        self.failed_file_details.append({
            'file_path': file_path,
            'error': str(error),
            'timestamp': datetime.now().isoformat()
        })

    def update_speed(self, files_per_minute: float):
        """更新处理速度"""
        self.processing_speeds.append(files_per_minute)

    def get_average_speed(self) -> float:
        """获取平均处理速度"""
        return sum(self.processing_speeds) / len(self.processing_speeds) if self.processing_speeds else 0

    def get_processing_time(self) -> float:
        """获取处理时间（秒）"""
        if self.start_time and self.end_time:
            return (self.end_time - self.start_time).total_seconds()
        elif self.start_time:
            return (datetime.now() - self.start_time).total_seconds()
        return 0

    def get_eta(self) -> str:
        """获取预估剩余时间"""
        if self.processed_files == 0:
            return "计算中..."
        
        avg_speed = self.get_average_speed()
        if avg_speed == 0:
            return "计算中..."
        
        remaining_files = self.total_files - self.processed_files
        eta_minutes = remaining_files / avg_speed
        
        if eta_minutes < 1:
            return f"{eta_minutes * 60:.0f}秒"
        elif eta_minutes < 60:
            return f"{eta_minutes:.1f}分钟"
        else:
            hours = eta_minutes / 60
            return f"{hours:.1f}小时"

class MemoryMonitor:
    """内存监控器"""
    def __init__(self, warning_threshold_gb: float = 8.0):
        self.warning_threshold = warning_threshold_gb * 1024 * 1024 * 1024  # 转换为字节
        self.process = psutil.Process()
        self.monitoring = False
        self.monitor_thread = None

    def start_monitoring(self):
        """开始监控内存"""
        self.monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()

    def stop_monitoring(self):
        """停止监控内存"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=1)

    def _monitor_loop(self):
        """内存监控循环"""
        while self.monitoring:
            try:
                memory_info = self.process.memory_info()
                memory_gb = memory_info.rss / 1024 / 1024 / 1024
                
                if memory_info.rss > self.warning_threshold:
                    logger.warning(f"内存使用过高: {memory_gb:.2f}GB")
                
                time.sleep(5)  # 每5秒检查一次
            except Exception as e:
                logger.error(f"内存监控错误: {e}")
                break

    def get_current_memory_gb(self) -> float:
        """获取当前内存使用量（GB）"""
        try:
            return self.process.memory_info().rss / 1024 / 1024 / 1024
        except:
            return 0.0

class ProgressEnhancedLogCleaner:
    """进度增强的日志清洗器"""
    
    def __init__(self, train_dir='files/train', test_dir='files/test'):
        self.train_dir = Path(train_dir)
        self.test_dir = Path(test_dir)
        self.stats = ProcessingStats()
        self.memory_monitor = MemoryMonitor()
        
        # 编译正则表达式模式
        self._compile_patterns()
        
        # 进度条相关
        self.main_pbar = None
        self.file_pbar = None
        self.pbar_lock = threading.Lock()

    def _compile_patterns(self):
        """编译正则表达式模式"""
        # 5G网元模式
        g5_nf_list = ['AMF', 'SMF', 'UPF', 'AUSF', 'UDM', 'UDR', 'PCF', 'NRF', 'NSSF', 'NEF',
                      'SEPP', 'SCP', 'BSF', 'CHF', 'NWDAF', 'AF', 'GMLC', 'LMF']
        
        self.compiled_patterns = {
            'g5_nf': re.compile(rf'\b({"|".join(g5_nf_list)})\b', re.IGNORECASE),
            'timestamp': re.compile(r'\d{4}-\d{2}-\d{2}[T ]\d{2}:\d{2}:\d{2}'),
            'log_level': re.compile(r'\[(ERROR|WARN|INFO|DEBUG|TRACE|FATAL)\]', re.IGNORECASE),
            'error_keywords': re.compile(r'(failed|failure|error|timeout|exception|denied|reject|abort)', re.IGNORECASE),
            'success_keywords': re.compile(r'(success|complete|established|registered)', re.IGNORECASE),
        }

        # 故障模式
        self.fault_patterns = {
            'connection_failure': re.compile(
                r'(connection\s+(failed|timeout|refused|reset|closed)|'
                r'unable\s+to\s+connect|network\s+unreachable)', re.IGNORECASE
            ),
            'authentication_failure': re.compile(
                r'(auth(entication)?\s+(failed|failure|error)|'
                r'invalid\s+(credentials|certificate|token))', re.IGNORECASE
            ),
            'resource_exhaustion': re.compile(
                r'(out\s+of\s+(memory|resources|capacity)|'
                r'resource\s+(exhausted|unavailable))', re.IGNORECASE
            ),
        }

    def _detect_encoding(self, file_path: str) -> str:
        """检测文件编码"""
        encodings = ['utf-8', 'gbk', 'gb2312', 'latin1']
        
        try:
            with open(file_path, 'rb') as f:
                raw_data = f.read(min(1024, os.path.getsize(file_path)))
            
            for encoding in encodings:
                try:
                    raw_data.decode(encoding)
                    return encoding
                except UnicodeDecodeError:
                    continue
        except Exception:
            pass
        
        return 'utf-8'

    def _read_file_with_progress(self, file_path: str, show_progress: bool = True) -> Optional[str]:
        """带进度显示的文件读取"""
        try:
            encoding = self._detect_encoding(file_path)
            file_size = os.path.getsize(file_path)
            
            # 对于大文件显示读取进度
            if file_size > 10 * 1024 * 1024 and show_progress:  # 大于10MB
                return self._read_large_file_with_progress(file_path, encoding, file_size)
            else:
                with open(file_path, 'r', encoding=encoding, buffering=8192) as f:
                    return f.read()
                    
        except Exception as e:
            logger.error(f"读取文件失败 {file_path}: {e}")
            return None

    def _read_large_file_with_progress(self, file_path: str, encoding: str, file_size: int) -> str:
        """带进度显示的大文件读取"""
        content_parts = []
        chunk_size = 1024 * 1024  # 1MB chunks
        
        with open(file_path, 'r', encoding=encoding) as f:
            with tqdm(total=file_size, unit='B', unit_scale=True, 
                     desc=f"读取 {Path(file_path).name}", leave=False) as pbar:
                while True:
                    chunk = f.read(chunk_size)
                    if not chunk:
                        break
                    content_parts.append(chunk)
                    pbar.update(len(chunk.encode(encoding)))
        
        return ''.join(content_parts)

    def _intelligent_line_sampling_with_progress(self, lines: List[str], max_lines: int = 5000) -> List[str]:
        """带进度显示的智能行采样"""
        if len(lines) <= max_lines:
            return [line.strip() for line in lines if line.strip()]

        # 分类行
        critical_lines = []
        important_lines = []
        normal_lines = []
        
        # 使用进度条处理行分类
        with tqdm(lines, desc="分析日志行", leave=False, disable=len(lines) < 50000) as pbar:
            for line in pbar:
                line = line.strip()
                if not line:
                    continue
                    
                line_upper = line.upper()
                
                # 关键错误和故障
                if any(pattern.search(line) for pattern in self.fault_patterns.values()) or \
                   any(level in line_upper for level in ['ERROR', 'FATAL', 'CRITICAL']):
                    critical_lines.append(line)
                # 重要5G业务流程
                elif self.compiled_patterns['g5_nf'].search(line) or \
                     any(nf in line_upper for nf in ['AMF', 'SMF', 'UPF']):
                    important_lines.append(line)
                else:
                    normal_lines.append(line)

        # 智能采样
        selected_lines = critical_lines[:]
        remaining_quota = max_lines - len(selected_lines)
        
        if remaining_quota > 0:
            # 保留重要行（采样）
            important_sample_size = min(len(important_lines), remaining_quota // 2)
            if important_sample_size > 0:
                step = max(1, len(important_lines) // important_sample_size)
                selected_lines.extend(important_lines[::step][:important_sample_size])
                remaining_quota -= important_sample_size
            
            # 保留普通行（采样）
            if remaining_quota > 0 and normal_lines:
                normal_sample_size = min(len(normal_lines), remaining_quota)
                step = max(1, len(normal_lines) // normal_sample_size)
                selected_lines.extend(normal_lines[::step][:normal_sample_size])

        return selected_lines

    def clean_log_content_with_progress(self, content: str, file_name: str = "") -> str:
        """带进度显示的日志内容清洗"""
        if not content or not content.strip():
            return ""

        original_size = len(content)
        self.stats.total_size_before += original_size

        # 1. 预处理
        content = re.sub(r'\r\n|\r', '\n', content)
        content = re.sub(r'[\x00-\x08\x0b\x0c\x0e-\x1f\x7f-\xff]', '', content)

        # 2. 智能行过滤和采样
        lines = content.split('\n')
        original_line_count = len(lines)
        self.stats.total_lines_processed += original_line_count

        cleaned_lines = self._intelligent_line_sampling_with_progress(lines)
        self.stats.total_lines_kept += len(cleaned_lines)

        # 3. 重新组合内容
        content = '\n'.join(cleaned_lines)

        # 4. 限制总字符数
        max_chars = 500000
        if len(content) > max_chars:
            content = content[:max_chars]
            last_newline = content.rfind('\n')
            if last_newline > 0:
                content = content[:last_newline]

        # 5. 标准化处理
        content = self._normalize_timestamps(content)
        content = self._normalize_log_levels(content)

        self.stats.total_size_after += len(content)
        return content

    def _normalize_timestamps(self, content: str) -> str:
        """标准化时间戳格式"""
        patterns_replacements = [
            (re.compile(r'(\d{4})-(\d{2})-(\d{2})T(\d{2}):(\d{2}):(\d{2})(?:\.\d+)?(?:Z|[+-]\d{2}:?\d{2})?'),
             r'\1-\2-\3 \4:\5:\6'),
            (re.compile(r'(\d{2})/(\d{2})/(\d{4}) (\d{2}):(\d{2}):(\d{2})'),
             r'\3-\1-\2 \4:\5:\6'),
        ]

        for pattern, replacement in patterns_replacements:
            content = pattern.sub(replacement, content)

        return content

    def _normalize_log_levels(self, content: str) -> str:
        """标准化日志级别格式"""
        level_patterns = [
            (re.compile(r'\b(TRACE|DEBUG|INFO|WARN|WARNING|ERROR|FATAL)\s*:', re.IGNORECASE), r'[\1]'),
            (re.compile(r'\b(TRACE|DEBUG|INFO|WARN|WARNING|ERROR|FATAL)\s*-', re.IGNORECASE), r'[\1]'),
            (re.compile(r'<(TRACE|DEBUG|INFO|WARN|WARNING|ERROR|FATAL)>', re.IGNORECASE), r'[\1]'),
        ]

        for pattern, replacement in level_patterns:
            content = pattern.sub(replacement, content)

        return content

    def _process_single_file_with_progress(self, file_info: Tuple[str, str, int]) -> Dict:
        """处理单个文件（带进度显示）"""
        input_file, output_file, file_index = file_info
        file_name = Path(input_file).name

        result = {
            'status': 'failed',
            'file_path': input_file,
            'file_name': file_name,
            'file_index': file_index,
            'error': None,
            'lines_processed': 0,
            'lines_kept': 0,
            'size_before': 0,
            'size_after': 0,
            'processing_time': 0
        }

        start_time = time.time()

        try:
            # 检查文件是否存在
            if not os.path.exists(input_file):
                result['error'] = "文件不存在"
                return result

            # 获取文件大小
            file_size = os.path.getsize(input_file)
            result['size_before'] = file_size

            # 读取文件内容
            content = self._read_file_with_progress(input_file, show_progress=file_size > 50*1024*1024)

            if not content:
                result['error'] = "文件读取失败或为空"
                result['status'] = 'empty'
                return result

            if not content.strip():
                result['error'] = "文件内容为空"
                result['status'] = 'empty'
                return result

            # 统计原始行数
            original_lines = len(content.split('\n'))
            result['lines_processed'] = original_lines

            # 清洗内容
            cleaned_content = self.clean_log_content_with_progress(content, file_name)

            if cleaned_content.strip():
                # 创建输出目录
                os.makedirs(os.path.dirname(output_file), exist_ok=True)

                # 保存清洗后的文件
                with open(output_file, 'w', encoding='utf-8') as f:
                    f.write(cleaned_content)

                result['status'] = 'success'
                result['size_after'] = len(cleaned_content)
                result['lines_kept'] = len(cleaned_content.split('\n'))
            else:
                result['status'] = 'empty'
                result['error'] = "清洗后内容为空"

        except Exception as e:
            result['error'] = str(e)
            logger.error(f"处理文件失败 {file_name}: {e}")

        finally:
            result['processing_time'] = time.time() - start_time

        return result

    def _update_progress_bars(self, result: Dict):
        """更新进度条"""
        with self.pbar_lock:
            if self.main_pbar:
                # 更新主进度条
                status_emoji = {
                    'success': '✅',
                    'failed': '❌',
                    'empty': '⚠️'
                }

                emoji = status_emoji.get(result['status'], '❓')
                file_name = result['file_name']
                memory_gb = self.memory_monitor.get_current_memory_gb()

                desc = f"{emoji} {file_name} | 内存: {memory_gb:.1f}GB | ETA: {self.stats.get_eta()}"
                self.main_pbar.set_description(desc)
                self.main_pbar.update(1)

                # 更新统计信息
                if result['status'] == 'success':
                    self.stats.processed_files += 1
                elif result['status'] == 'failed':
                    self.stats.failed_files += 1
                    self.stats.add_failed_file(result['file_path'], result['error'])
                elif result['status'] == 'empty':
                    self.stats.empty_files += 1

                # 更新处理速度
                elapsed_time = self.stats.get_processing_time() / 60  # 转换为分钟
                if elapsed_time > 0:
                    files_per_minute = (self.stats.processed_files + self.stats.failed_files + self.stats.empty_files) / elapsed_time
                    self.stats.update_speed(files_per_minute)

    def process_files_with_detailed_progress(self, file_list: List[Tuple[str, str]],
                                           output_dir: str, max_workers: int = 4) -> Dict:
        """带详细进度显示的文件处理"""

        self.stats.total_files = len(file_list)
        self.stats.start_time = datetime.now()

        # 启动内存监控
        self.memory_monitor.start_monitoring()

        # 为文件添加索引
        indexed_file_list = [(input_file, output_file, i)
                           for i, (input_file, output_file) in enumerate(file_list, 1)]

        logger.info(f"开始处理 {len(file_list)} 个文件，使用 {max_workers} 个线程")

        # 创建主进度条
        self.main_pbar = tqdm(
            total=len(file_list),
            desc="处理文件",
            unit="文件",
            position=0,
            bar_format='{l_bar}{bar}| {n_fmt}/{total_fmt} [{elapsed}<{remaining}, {rate_fmt}] {desc}'
        )

        try:
            # 并行处理文件
            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                # 提交所有任务
                future_to_file = {
                    executor.submit(self._process_single_file_with_progress, file_info): file_info
                    for file_info in indexed_file_list
                }

                # 处理完成的任务
                for future in as_completed(future_to_file):
                    try:
                        result = future.result()
                        self._update_progress_bars(result)

                    except Exception as e:
                        file_info = future_to_file[future]
                        logger.error(f"处理文件时发生异常 {file_info[0]}: {e}")
                        self.stats.failed_files += 1
                        self.stats.add_failed_file(file_info[0], str(e))

                        # 更新进度条
                        with self.pbar_lock:
                            if self.main_pbar:
                                self.main_pbar.update(1)

        finally:
            # 关闭进度条
            if self.main_pbar:
                self.main_pbar.close()

            # 停止内存监控
            self.memory_monitor.stop_monitoring()

            self.stats.end_time = datetime.now()

        # 返回处理结果
        return {
            'total_files': self.stats.total_files,
            'processed_files': self.stats.processed_files,
            'failed_files': self.stats.failed_files,
            'empty_files': self.stats.empty_files,
            'processing_time': self.stats.get_processing_time(),
            'average_speed': self.stats.get_average_speed(),
            'failed_file_details': self.stats.failed_file_details
        }

    def generate_detailed_report(self, output_dir: str) -> Dict:
        """生成详细的处理报告"""
        report = {
            'processing_summary': {
                'total_files': self.stats.total_files,
                'processed_files': self.stats.processed_files,
                'failed_files': self.stats.failed_files,
                'empty_files': self.stats.empty_files,
                'success_rate': (self.stats.processed_files / self.stats.total_files * 100) if self.stats.total_files > 0 else 0,
            },
            'data_statistics': {
                'total_lines_processed': self.stats.total_lines_processed,
                'total_lines_kept': self.stats.total_lines_kept,
                'line_retention_rate': (self.stats.total_lines_kept / self.stats.total_lines_processed * 100) if self.stats.total_lines_processed > 0 else 0,
                'total_size_before_mb': self.stats.total_size_before / 1024 / 1024,
                'total_size_after_mb': self.stats.total_size_after / 1024 / 1024,
                'compression_ratio': (1 - self.stats.total_size_after / self.stats.total_size_before) * 100 if self.stats.total_size_before > 0 else 0,
            },
            'performance_metrics': {
                'total_processing_time_seconds': self.stats.get_processing_time(),
                'total_processing_time_formatted': str(timedelta(seconds=int(self.stats.get_processing_time()))),
                'average_speed_files_per_minute': self.stats.get_average_speed(),
                'average_lines_per_second': (self.stats.total_lines_processed / self.stats.get_processing_time()) if self.stats.get_processing_time() > 0 else 0,
            },
            'failed_files': self.stats.failed_file_details,
            'timestamp': datetime.now().isoformat(),
        }

        # 保存报告
        report_file = Path(output_dir) / 'processing_report.json'
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)

        # 生成简化的文本报告
        self._generate_text_report(report, Path(output_dir) / 'processing_summary.txt')

        return report

    def _generate_text_report(self, report: Dict, output_file: Path):
        """生成文本格式的报告"""
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("=" * 60 + "\n")
            f.write("5G核心网日志处理报告\n")
            f.write("=" * 60 + "\n\n")

            # 处理摘要
            summary = report['processing_summary']
            f.write("📊 处理摘要:\n")
            f.write(f"  总文件数: {summary['total_files']}\n")
            f.write(f"  成功处理: {summary['processed_files']} ({summary['success_rate']:.1f}%)\n")
            f.write(f"  处理失败: {summary['failed_files']}\n")
            f.write(f"  空文件: {summary['empty_files']}\n\n")

            # 数据统计
            data_stats = report['data_statistics']
            f.write("📈 数据统计:\n")
            f.write(f"  处理行数: {data_stats['total_lines_processed']:,}\n")
            f.write(f"  保留行数: {data_stats['total_lines_kept']:,}\n")
            f.write(f"  行保留率: {data_stats['line_retention_rate']:.1f}%\n")
            f.write(f"  原始大小: {data_stats['total_size_before_mb']:.1f} MB\n")
            f.write(f"  清洗后大小: {data_stats['total_size_after_mb']:.1f} MB\n")
            f.write(f"  压缩率: {data_stats['compression_ratio']:.1f}%\n\n")

            # 性能指标
            perf = report['performance_metrics']
            f.write("⚡ 性能指标:\n")
            f.write(f"  总处理时间: {perf['total_processing_time_formatted']}\n")
            f.write(f"  处理速度: {perf['average_speed_files_per_minute']:.1f} 文件/分钟\n")
            f.write(f"  行处理速度: {perf['average_lines_per_second']:.0f} 行/秒\n\n")

            # 失败文件详情
            if report['failed_files']:
                f.write("❌ 失败文件详情:\n")
                for failed in report['failed_files']:
                    f.write(f"  {failed['file_path']}: {failed['error']}\n")

    def print_final_summary(self, report: Dict):
        """打印最终摘要"""
        print("\n" + "=" * 80)
        print("🎉 5G核心网日志处理完成!")
        print("=" * 80)

        summary = report['processing_summary']
        data_stats = report['data_statistics']
        perf = report['performance_metrics']

        print(f"\n📊 处理结果:")
        print(f"   ✅ 成功处理: {summary['processed_files']}/{summary['total_files']} 文件 ({summary['success_rate']:.1f}%)")
        print(f"   ❌ 处理失败: {summary['failed_files']} 文件")
        print(f"   ⚠️  空文件: {summary['empty_files']} 文件")

        print(f"\n📈 数据统计:")
        print(f"   📝 处理日志行数: {data_stats['total_lines_processed']:,} 行")
        print(f"   📝 保留日志行数: {data_stats['total_lines_kept']:,} 行 ({data_stats['line_retention_rate']:.1f}%)")
        print(f"   💾 数据压缩: {data_stats['total_size_before_mb']:.1f}MB → {data_stats['total_size_after_mb']:.1f}MB ({data_stats['compression_ratio']:.1f}%)")

        print(f"\n⚡ 性能表现:")
        print(f"   ⏱️  总处理时间: {perf['total_processing_time_formatted']}")
        print(f"   🚀 处理速度: {perf['average_speed_files_per_minute']:.1f} 文件/分钟")
        print(f"   🚀 行处理速度: {perf['average_lines_per_second']:.0f} 行/秒")

        if summary['failed_files'] > 0:
            print(f"\n⚠️  注意: 有 {summary['failed_files']} 个文件处理失败，详情请查看处理报告")

        print("\n" + "=" * 80)


def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description='5G核心网日志清洗工具 - 进度增强版')
    parser.add_argument('--input-dir', required=True, help='输入目录路径')
    parser.add_argument('--output-dir', required=True, help='输出目录路径')
    parser.add_argument('--workers', type=int, default=4, help='并行处理线程数')
    parser.add_argument('--file-pattern', default='*.txt', help='文件匹配模式')

    args = parser.parse_args()

    # 初始化清洗器
    cleaner = ProgressEnhancedLogCleaner()

    # 收集文件列表
    input_path = Path(args.input_dir)
    output_path = Path(args.output_dir)
    output_path.mkdir(parents=True, exist_ok=True)

    # 查找所有txt文件
    txt_files = list(input_path.glob(args.file_pattern))

    if not txt_files:
        print(f"❌ 在目录 {input_path} 中没有找到匹配 {args.file_pattern} 的文件")
        return

    print(f"🔍 找到 {len(txt_files)} 个文件需要处理")

    # 构建文件列表
    file_list = []
    for txt_file in txt_files:
        output_file = output_path / txt_file.name
        file_list.append((str(txt_file), str(output_file)))

    # 开始处理
    print(f"🚀 开始处理，使用 {args.workers} 个并行线程...")

    try:
        results = cleaner.process_files_with_detailed_progress(
            file_list, str(output_path), max_workers=args.workers
        )

        # 生成报告
        report = cleaner.generate_detailed_report(str(output_path))

        # 打印最终摘要
        cleaner.print_final_summary(report)

        print(f"\n📄 详细报告已保存到: {output_path / 'processing_report.json'}")
        print(f"📄 摘要报告已保存到: {output_path / 'processing_summary.txt'}")

    except KeyboardInterrupt:
        print("\n⚠️  处理被用户中断")
    except Exception as e:
        print(f"\n❌ 处理过程中发生错误: {e}")
        logger.exception("处理过程中发生异常")


if __name__ == "__main__":
    main()
