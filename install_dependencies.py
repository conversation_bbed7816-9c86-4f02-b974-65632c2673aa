#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
5G核心网日志故障分类竞赛 - 依赖安装脚本
"""

import subprocess
import sys
import importlib

def install_package(package):
    """安装Python包"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✅ {package} 安装成功")
        return True
    except subprocess.CalledProcessError:
        print(f"❌ {package} 安装失败")
        return False

def check_package(package_name, import_name=None):
    """检查包是否已安装"""
    if import_name is None:
        import_name = package_name
    
    try:
        importlib.import_module(import_name)
        print(f"✅ {package_name} 已安装")
        return True
    except ImportError:
        print(f"❌ {package_name} 未安装")
        return False

def main():
    """主函数"""
    print("🚀 5G核心网日志故障分类竞赛 - 依赖检查与安装")
    print("="*60)
    
    # 必需的包列表
    required_packages = [
        ("pandas", "pandas"),
        ("numpy", "numpy"),
        ("scikit-learn", "sklearn"),
        ("xgboost", "xgboost"),
        ("lightgbm", "lightgbm"),
        ("imbalanced-learn", "imblearn"),
        ("jieba", "jieba")
    ]
    
    print("📋 检查已安装的包...")
    
    missing_packages = []
    
    for package_name, import_name in required_packages:
        if not check_package(package_name, import_name):
            missing_packages.append(package_name)
    
    if not missing_packages:
        print("\n🎉 所有依赖包都已安装!")
        return
    
    print(f"\n📦 需要安装 {len(missing_packages)} 个包:")
    for package in missing_packages:
        print(f"   - {package}")
    
    # 询问是否安装
    try:
        response = input(f"\n是否现在安装这些包? (y/N): ")
        if response.lower() != 'y':
            print("❌ 已取消安装")
            return
    except KeyboardInterrupt:
        print("\n❌ 已取消安装")
        return
    
    print("\n🔧 开始安装依赖包...")
    
    success_count = 0
    for package in missing_packages:
        print(f"\n安装 {package}...")
        if install_package(package):
            success_count += 1
    
    print(f"\n📊 安装结果:")
    print(f"   成功: {success_count}/{len(missing_packages)}")
    
    if success_count == len(missing_packages):
        print("🎉 所有依赖包安装完成!")
        print("\n现在可以运行竞赛解决方案:")
        print("python 5g_fault_classification_solution.py")
    else:
        print("⚠️  部分包安装失败，请手动安装:")
        failed_packages = [pkg for pkg in missing_packages 
                          if not check_package(pkg.split()[0])]
        for package in failed_packages:
            print(f"   pip install {package}")

if __name__ == "__main__":
    main()
