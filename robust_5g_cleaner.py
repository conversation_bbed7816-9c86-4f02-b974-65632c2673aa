#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
5G核心网日志清洗工具 - 内存稳健版本
专门解决OOM问题和实现断点续传

主要改进:
1. 更严格的内存监控和泄漏检测
2. 断点续传功能
3. 更保守的内存使用策略
4. 实时内存峰值检测
5. 紧急内存清理机制
"""

import os
import gc
import time
import psutil
import tracemalloc
import sys
from pathlib import Path
from typing import List, Dict, Tuple, Optional
import re
import json
import threading
from collections import deque

# 更保守的内存配置
MEMORY_LIMIT_GB = 8.0      # 降低到8GB，留出更大安全余量
MEMORY_WARNING_GB = 6.0    # 6GB时开始警告
MEMORY_CRITICAL_GB = 10.0  # 10GB时紧急清理
CHUNK_SIZE = 256 * 1024    # 减小到256KB
MAX_LINES_PER_FILE = 2000  # 进一步减少保留行数
MAX_CHARS_PER_FILE = 200000 # 减少字符数限制
BATCH_SIZE = 5             # 每批处理5个文件后强制清理

class AdvancedMemoryMonitor:
    """高级内存监控器，检测内存泄漏和峰值"""
    
    def __init__(self):
        self.process = psutil.Process()
        self.peak_memory = 0
        self.memory_history = deque(maxlen=50)  # 保留最近50次内存记录
        self.leak_threshold = 0.1  # 100MB泄漏阈值
        self.monitoring = False
        self.monitor_thread = None
        
        # 启用内存跟踪
        tracemalloc.start()
    
    def get_memory_info(self) -> Dict:
        """获取详细内存信息"""
        try:
            memory_info = self.process.memory_info()
            memory_gb = memory_info.rss / (1024**3)
            
            # 获取系统内存信息
            system_memory = psutil.virtual_memory()
            
            # 获取tracemalloc信息
            current, peak = tracemalloc.get_traced_memory()
            
            info = {
                'rss_gb': memory_gb,
                'vms_gb': memory_info.vms / (1024**3),
                'system_available_gb': system_memory.available / (1024**3),
                'system_used_percent': system_memory.percent,
                'tracemalloc_current_mb': current / (1024**2),
                'tracemalloc_peak_mb': peak / (1024**2)
            }
            
            self.peak_memory = max(self.peak_memory, memory_gb)
            self.memory_history.append(memory_gb)
            
            return info
            
        except Exception as e:
            return {'error': str(e), 'rss_gb': 0}
    
    def detect_memory_leak(self) -> bool:
        """检测内存泄漏"""
        if len(self.memory_history) < 10:
            return False
        
        # 检查最近10次内存使用是否持续增长
        recent_memory = list(self.memory_history)[-10:]
        trend = sum(recent_memory[i] - recent_memory[i-1] for i in range(1, len(recent_memory)))
        
        return trend > self.leak_threshold
    
    def is_memory_critical(self) -> bool:
        """检查内存是否达到临界状态"""
        info = self.get_memory_info()
        
        # 多重检查
        conditions = [
            info['rss_gb'] > MEMORY_CRITICAL_GB,
            info['system_used_percent'] > 90,
            info['system_available_gb'] < 2.0,
            self.detect_memory_leak()
        ]
        
        return any(conditions)
    
    def is_memory_safe(self) -> bool:
        """检查内存是否安全"""
        info = self.get_memory_info()
        return info['rss_gb'] <= MEMORY_LIMIT_GB and info['system_available_gb'] > 4.0
    
    def emergency_cleanup(self):
        """紧急内存清理"""
        print("⚠️  执行紧急内存清理...")
        
        # 多次垃圾回收
        for _ in range(3):
            gc.collect()
            time.sleep(0.1)
        
        # 清理tracemalloc
        tracemalloc.clear_traces()
        
        # 强制Python释放内存给系统
        if hasattr(gc, 'set_threshold'):
            gc.set_threshold(0)  # 禁用自动垃圾回收
            gc.collect()
            gc.set_threshold(700, 10, 10)  # 恢复默认设置

class RobustLogCleaner:
    """内存稳健的5G日志清洗器"""
    
    def __init__(self):
        self.memory_monitor = AdvancedMemoryMonitor()
        self.stats = {
            'total_files': 0,
            'processed_files': 0,
            'failed_files': 0,
            'skipped_files': 0,
            'total_lines_processed': 0,
            'total_lines_kept': 0,
            'start_time': None,
            'failed_files_list': [],
            'memory_cleanups': 0
        }
        self._compile_patterns()
    
    def _compile_patterns(self):
        """编译正则表达式模式"""
        g5_nf = 'AMF|SMF|UPF|AUSF|UDM|UDR|PCF|NRF|NSSF|NEF'
        
        self.patterns = {
            'g5_nf': re.compile(rf'\b({g5_nf})\b', re.IGNORECASE),
            'error': re.compile(r'\[(ERROR|FATAL|CRITICAL)\]', re.IGNORECASE),
            'warning': re.compile(r'\[(WARN|WARNING)\]', re.IGNORECASE),
            'fault_keywords': re.compile(
                r'(failed|failure|error|timeout|exception|denied|reject|abort)',
                re.IGNORECASE
            )
        }
    
    def _safe_file_read(self, file_path: str) -> Optional[str]:
        """安全的文件读取，带内存检查"""
        try:
            file_size = os.path.getsize(file_path)
            
            # 超大文件直接跳过
            if file_size > 100 * 1024 * 1024:  # 100MB
                print(f"   跳过超大文件: {Path(file_path).name} ({file_size/1024/1024:.1f}MB)")
                return None
            
            # 检查内存状态
            if not self.memory_monitor.is_memory_safe():
                self.memory_monitor.emergency_cleanup()
                time.sleep(0.5)
                
                if not self.memory_monitor.is_memory_safe():
                    print(f"   内存不足，跳过文件: {Path(file_path).name}")
                    return None
            
            # 检测编码
            encoding = self._detect_encoding_fast(file_path)
            
            # 分块读取
            content_parts = []
            max_read = min(file_size, 20 * 1024 * 1024)  # 最多20MB
            
            with open(file_path, 'r', encoding=encoding, buffering=CHUNK_SIZE) as f:
                read_size = 0
                while read_size < max_read:
                    chunk = f.read(CHUNK_SIZE)
                    if not chunk:
                        break
                    
                    content_parts.append(chunk)
                    read_size += len(chunk.encode(encoding))
                    
                    # 每读取1MB检查一次内存
                    if read_size % (1024 * 1024) == 0:
                        if self.memory_monitor.is_memory_critical():
                            print(f"   内存临界，停止读取: {Path(file_path).name}")
                            break
            
            return ''.join(content_parts)
            
        except Exception as e:
            print(f"   读取失败: {Path(file_path).name} - {e}")
            return None
    
    def _detect_encoding_fast(self, file_path: str) -> str:
        """快速编码检测"""
        try:
            with open(file_path, 'rb') as f:
                sample = f.read(512)  # 只读512字节
            
            for encoding in ['utf-8', 'gbk', 'latin1']:
                try:
                    sample.decode(encoding)
                    return encoding
                except UnicodeDecodeError:
                    continue
            return 'utf-8'
        except:
            return 'utf-8'
    
    def _ultra_conservative_sampling(self, lines: List[str]) -> List[str]:
        """超保守的行采样策略"""
        if len(lines) <= MAX_LINES_PER_FILE:
            return [line.strip() for line in lines if line.strip()]
        
        # 只保留最关键的行
        critical_lines = []
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            # 只保留错误和5G网元相关的关键行
            if (self.patterns['error'].search(line) or 
                self.patterns['fault_keywords'].search(line) or
                (self.patterns['g5_nf'].search(line) and 
                 ('failed' in line.lower() or 'error' in line.lower()))):
                critical_lines.append(line)
                
                if len(critical_lines) >= MAX_LINES_PER_FILE:
                    break
        
        # 如果关键行不够，补充一些5G网元行
        if len(critical_lines) < MAX_LINES_PER_FILE // 2:
            for line in lines:
                line = line.strip()
                if (line and self.patterns['g5_nf'].search(line) and 
                    line not in critical_lines):
                    critical_lines.append(line)
                    if len(critical_lines) >= MAX_LINES_PER_FILE:
                        break
        
        return critical_lines[:MAX_LINES_PER_FILE]
    
    def _minimal_clean_content(self, content: str) -> str:
        """最小化的内容清洗"""
        if not content or not content.strip():
            return ""
        
        # 基础清理
        content = re.sub(r'\r\n|\r', '\n', content)
        content = re.sub(r'[\x00-\x08\x0b\x0c\x0e-\x1f\x7f-\xff]', '', content)
        
        lines = content.split('\n')
        original_count = len(lines)
        
        # 超保守采样
        cleaned_lines = self._ultra_conservative_sampling(lines)
        content = '\n'.join(cleaned_lines)
        
        # 限制字符数
        if len(content) > MAX_CHARS_PER_FILE:
            content = content[:MAX_CHARS_PER_FILE]
            last_newline = content.rfind('\n')
            if last_newline > 0:
                content = content[:last_newline]
        
        # 更新统计
        self.stats['total_lines_processed'] += original_count
        self.stats['total_lines_kept'] += len(cleaned_lines)
        
        return content
    
    def _process_single_file_safe(self, input_file: str, output_file: str) -> str:
        """安全处理单个文件"""
        file_name = Path(input_file).name
        
        try:
            # 检查输出文件是否已存在（断点续传）
            if os.path.exists(output_file) and os.path.getsize(output_file) > 0:
                return 'skipped'
            
            # 内存安全检查
            if self.memory_monitor.is_memory_critical():
                self.memory_monitor.emergency_cleanup()
                self.stats['memory_cleanups'] += 1
                time.sleep(1)
                
                if self.memory_monitor.is_memory_critical():
                    return 'memory_critical'
            
            # 读取文件
            content = self._safe_file_read(input_file)
            if not content:
                return 'read_failed'
            
            # 清洗内容
            cleaned_content = self._minimal_clean_content(content)
            
            # 立即释放原始内容
            del content
            gc.collect()
            
            if cleaned_content.strip():
                # 创建输出目录
                os.makedirs(os.path.dirname(output_file), exist_ok=True)
                
                # 保存文件
                with open(output_file, 'w', encoding='utf-8') as f:
                    f.write(cleaned_content)
                
                # 立即释放清洗内容
                del cleaned_content
                gc.collect()
                
                return 'success'
            else:
                del cleaned_content
                return 'empty'
                
        except Exception as e:
            error_msg = f"{file_name}: {str(e)}"
            self.stats['failed_files_list'].append(error_msg)
            print(f"   处理异常: {error_msg}")
            return 'failed'
        finally:
            # 强制内存清理
            gc.collect()
    
    def process_files_robust(self, input_dir: str, output_dir: str, 
                           file_pattern: str = "*.txt") -> Dict:
        """稳健的文件处理，支持断点续传"""
        
        input_path = Path(input_dir)
        output_path = Path(output_dir)
        
        if not input_path.exists():
            return {"error": f"输入目录不存在: {input_dir}"}
        
        txt_files = list(input_path.glob(file_pattern))
        if not txt_files:
            return {"error": f"没有找到匹配的文件"}
        
        output_path.mkdir(parents=True, exist_ok=True)
        
        # 检查已处理的文件（断点续传）
        existing_files = set(f.name for f in output_path.glob("*.txt") if f.stat().st_size > 0)
        remaining_files = [f for f in txt_files if f.name not in existing_files]
        
        print(f"总文件: {len(txt_files)}, 已完成: {len(existing_files)}, 待处理: {len(remaining_files)}")
        
        if not remaining_files:
            print("所有文件已处理完成!")
            return self._get_final_results(len(txt_files), len(existing_files))
        
        # 初始化统计
        self.stats['total_files'] = len(remaining_files)
        self.stats['start_time'] = time.time()
        
        print(f"开始处理剩余 {len(remaining_files)} 个文件...")
        
        # 分批处理
        for batch_start in range(0, len(remaining_files), BATCH_SIZE):
            batch_end = min(batch_start + BATCH_SIZE, len(remaining_files))
            batch_files = remaining_files[batch_start:batch_end]
            
            print(f"\n处理批次 {batch_start//BATCH_SIZE + 1}: 文件 {batch_start+1}-{batch_end}")
            
            for i, txt_file in enumerate(batch_files):
                global_index = batch_start + i + 1
                output_file = output_path / txt_file.name
                
                # 显示进度
                progress = global_index / len(remaining_files) * 100
                memory_info = self.memory_monitor.get_memory_info()
                
                print(f"[{global_index:3d}/{len(remaining_files)}] {progress:5.1f}% | "
                      f"{txt_file.name} | 内存: {memory_info['rss_gb']:.1f}GB | "
                      f"系统可用: {memory_info['system_available_gb']:.1f}GB")
                
                # 处理文件
                result = self._process_single_file_safe(str(txt_file), str(output_file))
                
                if result == 'success':
                    self.stats['processed_files'] += 1
                elif result == 'skipped':
                    self.stats['skipped_files'] += 1
                elif result == 'memory_critical':
                    print(f"⚠️  内存临界，停止处理。已完成: {self.stats['processed_files']} 个文件")
                    break
                else:
                    self.stats['failed_files'] += 1
            
            # 批次完成后强制清理
            print(f"   批次完成，执行内存清理...")
            self.memory_monitor.emergency_cleanup()
            time.sleep(2)  # 等待系统回收内存
            
            # 检查是否需要停止
            if self.memory_monitor.is_memory_critical():
                print("⚠️  内存压力过大，暂停处理")
                break
        
        return self._get_final_results(len(txt_files), len(existing_files))
    
    def _get_final_results(self, total_original_files: int, already_completed: int) -> Dict:
        """获取最终结果"""
        elapsed_time = time.time() - self.stats['start_time'] if self.stats['start_time'] else 0
        
        total_completed = already_completed + self.stats['processed_files']
        
        results = {
            'total_files': total_original_files,
            'already_completed': already_completed,
            'newly_processed': self.stats['processed_files'],
            'total_completed': total_completed,
            'failed_files': self.stats['failed_files'],
            'skipped_files': self.stats['skipped_files'],
            'completion_rate': total_completed / total_original_files * 100,
            'processing_time_minutes': elapsed_time / 60,
            'peak_memory_gb': self.memory_monitor.peak_memory,
            'memory_cleanups': self.stats['memory_cleanups'],
            'failed_files_list': self.stats['failed_files_list']
        }
        
        # 打印摘要
        print(f"\n处理完成!")
        print(f"总进度: {total_completed}/{total_original_files} ({results['completion_rate']:.1f}%)")
        print(f"本次处理: {self.stats['processed_files']} 个文件")
        print(f"失败: {self.stats['failed_files']} 个")
        print(f"用时: {results['processing_time_minutes']:.1f} 分钟")
        print(f"峰值内存: {results['peak_memory_gb']:.1f}GB")
        print(f"内存清理次数: {self.stats['memory_cleanups']}")
        
        if self.stats['failed_files'] > 0:
            print(f"\n失败文件:")
            for failed in self.stats['failed_files_list'][:5]:
                print(f"  - {failed}")
        
        return results
