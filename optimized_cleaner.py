# ============================================================================
# 5G核心网日志数据清洗工具 - 优化版本
# ============================================================================

import os
import re
import json
import pandas as pd
import numpy as np
from collections import Counter, defaultdict
from datetime import datetime
import pickle
import gc
import time
from tqdm import tqdm
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
from functools import lru_cache
import mmap
import logging
from typing import List, Dict, Tuple, Optional, Union
from dataclasses import dataclass, field
from pathlib import Path

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class CleaningConfig:
    """清洗配置类"""
    max_lines_per_file: int = 5000
    max_chars_per_file: int = 500000
    max_file_size_mb: int = 10
    max_workers: int = 4
    chunk_size: int = 1024 * 1024  # 1MB chunks
    preserve_error_logs: bool = True
    enable_parallel_processing: bool = True
    
    # 5G网元和协议配置
    g5_network_functions: List[str] = field(default_factory=lambda: [
        'AMF', 'SMF', 'UPF', 'AUSF', 'UDM', 'UDR', 'PCF', 'NRF', 'NSSF', 'NEF',
        'SEPP', 'SCP', 'BSF', 'CHF', 'NWDAF', 'AF', 'GMLC', 'LMF', 'UDSF', 'UNEF'
    ])
    
    # 5G接口和协议
    g5_interfaces: List[str] = field(default_factory=lambda: [
        'N1', 'N2', 'N3', 'N4', 'N6', 'N7', 'N8', 'N9', 'N10', 'N11', 'N12', 'N13', 'N14', 'N15', 'N16', 'N17',
        'Namf', 'Nsmf', 'Nupf', 'Nausf', 'Nudm', 'Nudr', 'Npcf', 'Nnrf', 'Nnssf', 'Nnef',
        'NGAP', 'HTTP2', 'SBI', 'PFCP', 'GTP-U', 'SCTP', 'DIAMETER'
    ])
    
    # 5G业务流程
    g5_procedures: List[str] = field(default_factory=lambda: [
        'Registration', 'Deregistration', 'Authentication', 'Authorization', 'Handover',
        'PDU_Session_Establishment', 'PDU_Session_Modification', 'PDU_Session_Release',
        'Service_Request', 'UE_Context_Transfer', 'Path_Switch', 'Xn_Handover', 'N2_Handover'
    ])

class OptimizedMemoryFriendlyLogCleaner:
    def __init__(self, train_dir='files/train', test_dir='files/test', config: CleaningConfig = None):
        self.train_dir = Path(train_dir)
        self.test_dir = Path(test_dir)
        self.config = config or CleaningConfig()
        
        # 统计信息
        self.cleaning_stats = defaultdict(int)
        
        # 编译正则表达式模式以提高性能
        self._compile_patterns()
        
        # 初始化故障分类相关的模式
        self._init_fault_patterns()

    def _compile_patterns(self):
        """编译正则表达式模式以提高匹配性能"""
        # 5G网元模式
        nf_pattern = '|'.join(self.config.g5_network_functions)
        interface_pattern = '|'.join(self.config.g5_interfaces)
        procedure_pattern = '|'.join(self.config.g5_procedures)
        
        self.compiled_patterns = {
            'g5_nf': re.compile(rf'\b({nf_pattern})\b', re.IGNORECASE),
            'g5_interface': re.compile(rf'\b({interface_pattern})\b', re.IGNORECASE),
            'g5_procedure': re.compile(rf'\b({procedure_pattern})\b', re.IGNORECASE),
            'timestamp': re.compile(r'\d{4}-\d{2}-\d{2}[T ]\d{2}:\d{2}:\d{2}'),
            'log_level': re.compile(r'\[(ERROR|WARN|INFO|DEBUG|TRACE|FATAL)\]', re.IGNORECASE),
            'ip_address': re.compile(r'\b(?:\d{1,3}\.){3}\d{1,3}\b'),
            'session_id': re.compile(r'[Ss]ession[_\-\s]*[Ii][Dd][:=]\s*([a-fA-F0-9\-]+)'),
            'imsi': re.compile(r'IMSI[:=]\s*(\d{15})'),
            'supi': re.compile(r'SUPI[:=]\s*([a-zA-Z0-9\-]+)'),
        }

    def _init_fault_patterns(self):
        """初始化故障分类相关的模式"""
        self.fault_patterns = {
            'connection_failure': re.compile(
                r'(connection\s+(failed|timeout|refused|reset|closed)|'
                r'unable\s+to\s+connect|network\s+unreachable|'
                r'connection\s+timed\s+out)', re.IGNORECASE
            ),
            'authentication_failure': re.compile(
                r'(auth(entication)?\s+(failed|failure|error)|'
                r'invalid\s+(credentials|certificate|token)|'
                r'unauthorized|forbidden)', re.IGNORECASE
            ),
            'resource_exhaustion': re.compile(
                r'(out\s+of\s+(memory|resources|capacity)|'
                r'resource\s+(exhausted|unavailable)|'
                r'quota\s+exceeded|limit\s+reached)', re.IGNORECASE
            ),
            'protocol_error': re.compile(
                r'(protocol\s+(error|violation|mismatch)|'
                r'invalid\s+(message|format|syntax)|'
                r'malformed\s+(packet|request|response))', re.IGNORECASE
            ),
            'service_unavailable': re.compile(
                r'(service\s+(unavailable|down|unreachable)|'
                r'server\s+(error|unavailable)|'
                r'endpoint\s+not\s+found)', re.IGNORECASE
            )
        }

    @lru_cache(maxsize=1000)
    def _detect_encoding(self, file_path: str) -> str:
        """检测文件编码（带缓存）"""
        encodings = ['utf-8', 'gbk', 'gb2312', 'latin1', 'cp1252']
        
        try:
            with open(file_path, 'rb') as f:
                raw_data = f.read(min(1024, os.path.getsize(file_path)))
            
            for encoding in encodings:
                try:
                    raw_data.decode(encoding)
                    return encoding
                except UnicodeDecodeError:
                    continue
        except Exception as e:
            logger.warning(f"编码检测失败 {file_path}: {e}")
        
        return 'utf-8'  # 默认编码

    def _read_file_efficiently(self, file_path: str) -> Optional[str]:
        """高效读取文件内容"""
        try:
            encoding = self._detect_encoding(file_path)
            file_size = os.path.getsize(file_path)
            
            # 对于大文件使用内存映射
            if file_size > self.config.chunk_size:
                return self._read_large_file_with_mmap(file_path, encoding)
            else:
                with open(file_path, 'r', encoding=encoding, buffering=8192) as f:
                    return f.read()
                    
        except Exception as e:
            logger.error(f"读取文件失败 {file_path}: {e}")
            return None

    def _read_large_file_with_mmap(self, file_path: str, encoding: str) -> str:
        """使用内存映射读取大文件"""
        try:
            with open(file_path, 'r', encoding=encoding) as f:
                with mmap.mmap(f.fileno(), 0, access=mmap.ACCESS_READ) as mmapped_file:
                    return mmapped_file.read().decode(encoding)
        except Exception as e:
            logger.warning(f"内存映射读取失败，回退到普通读取 {file_path}: {e}")
            with open(file_path, 'r', encoding=encoding) as f:
                return f.read()

    def extract_enhanced_features(self, content: str) -> Dict[str, Union[int, float, List]]:
        """提取增强的5G日志特征"""
        if not content:
            return self._get_empty_features()
        
        lines = content.split('\n')
        non_empty_lines = [line.strip() for line in lines if line.strip()]
        content_upper = content.upper()
        total_lines = max(len(non_empty_lines), 1)
        
        features = {}
        
        # 基础统计特征
        features.update({
            'total_lines': len(non_empty_lines),
            'total_chars': len(content),
            'avg_line_length': np.mean([len(line) for line in non_empty_lines]) if non_empty_lines else 0,
            'max_line_length': max([len(line) for line in non_empty_lines]) if non_empty_lines else 0,
            'min_line_length': min([len(line) for line in non_empty_lines]) if non_empty_lines else 0,
            'unique_lines_ratio': len(set(non_empty_lines)) / total_lines if total_lines > 0 else 0,
        })
        
        # 日志级别分布
        log_levels = ['ERROR', 'WARN', 'INFO', 'DEBUG', 'TRACE', 'FATAL']
        for level in log_levels:
            count = len(self.compiled_patterns['log_level'].findall(content))
            features[f'{level.lower()}_count'] = count
            features[f'{level.lower()}_ratio'] = count / total_lines
        
        # 5G网元统计
        nf_matches = self.compiled_patterns['g5_nf'].findall(content)
        features['g5_nf_count'] = len(nf_matches)
        features['g5_nf_types'] = len(set(nf_matches))
        features['g5_nf_diversity'] = features['g5_nf_types'] / len(self.config.g5_network_functions)
        
        # 5G接口统计
        interface_matches = self.compiled_patterns['g5_interface'].findall(content)
        features['g5_interface_count'] = len(interface_matches)
        features['g5_interface_types'] = len(set(interface_matches))
        
        # 5G业务流程统计
        procedure_matches = self.compiled_patterns['g5_procedure'].findall(content)
        features['g5_procedure_count'] = len(procedure_matches)
        features['g5_procedure_types'] = len(set(procedure_matches))
        
        # 故障模式检测
        for fault_type, pattern in self.fault_patterns.items():
            matches = pattern.findall(content)
            features[f'fault_{fault_type}_count'] = len(matches)
            features[f'fault_{fault_type}_ratio'] = len(matches) / total_lines
        
        # 时间相关特征
        timestamp_matches = self.compiled_patterns['timestamp'].findall(content)
        features['timestamp_count'] = len(timestamp_matches)
        features['has_timestamps'] = len(timestamp_matches) > 0
        
        # 网络相关特征
        ip_matches = self.compiled_patterns['ip_address'].findall(content)
        features['ip_address_count'] = len(ip_matches)
        features['unique_ip_count'] = len(set(ip_matches))
        
        # 会话相关特征
        session_matches = self.compiled_patterns['session_id'].findall(content)
        features['session_id_count'] = len(session_matches)
        features['unique_session_count'] = len(set(session_matches))
        
        return features

    def _get_empty_features(self) -> Dict[str, Union[int, float]]:
        """返回空特征字典"""
        return {
            'total_lines': 0, 'total_chars': 0, 'avg_line_length': 0,
            'max_line_length': 0, 'min_line_length': 0, 'unique_lines_ratio': 0,
            'error_count': 0, 'warn_count': 0, 'info_count': 0, 'debug_count': 0,
            'trace_count': 0, 'fatal_count': 0, 'error_ratio': 0, 'warn_ratio': 0,
            'info_ratio': 0, 'debug_ratio': 0, 'trace_ratio': 0, 'fatal_ratio': 0,
            'g5_nf_count': 0, 'g5_nf_types': 0, 'g5_nf_diversity': 0,
            'g5_interface_count': 0, 'g5_interface_types': 0,
            'g5_procedure_count': 0, 'g5_procedure_types': 0,
            'fault_connection_failure_count': 0, 'fault_authentication_failure_count': 0,
            'fault_resource_exhaustion_count': 0, 'fault_protocol_error_count': 0,
            'fault_service_unavailable_count': 0, 'fault_connection_failure_ratio': 0,
            'fault_authentication_failure_ratio': 0, 'fault_resource_exhaustion_ratio': 0,
            'fault_protocol_error_ratio': 0, 'fault_service_unavailable_ratio': 0,
            'timestamp_count': 0, 'has_timestamps': False, 'ip_address_count': 0,
            'unique_ip_count': 0, 'session_id_count': 0, 'unique_session_count': 0
        }

    def clean_log_content_optimized(self, content: str) -> str:
        """优化的日志内容清洗"""
        if not content or not content.strip():
            return ""

        original_size = len(content)
        self.cleaning_stats['total_size_before'] += original_size

        # 1. 预处理：标准化换行符和移除控制字符
        content = re.sub(r'\r\n|\r', '\n', content)
        content = re.sub(r'[\x00-\x08\x0b\x0c\x0e-\x1f\x7f-\xff]', '', content)

        # 2. 智能行过滤和采样
        lines = content.split('\n')
        cleaned_lines = self._intelligent_line_sampling(lines)

        # 3. 重新组合内容
        content = '\n'.join(cleaned_lines)

        # 4. 限制总字符数
        if len(content) > self.config.max_chars_per_file:
            content = content[:self.config.max_chars_per_file]
            last_newline = content.rfind('\n')
            if last_newline > 0:
                content = content[:last_newline]

        # 5. 标准化时间戳和日志级别
        content = self._normalize_timestamps_optimized(content)
        content = self._normalize_log_levels_optimized(content)

        self.cleaning_stats['total_size_after'] += len(content)
        return content

    def _intelligent_line_sampling(self, lines: List[str]) -> List[str]:
        """智能行采样策略"""
        if len(lines) <= self.config.max_lines_per_file:
            return [line.strip() for line in lines if line.strip()]

        # 分类行
        critical_lines = []  # 关键错误和故障
        important_lines = []  # 重要业务流程
        normal_lines = []    # 普通日志

        for line in lines:
            line = line.strip()
            if not line:
                continue

            line_upper = line.upper()

            # 关键错误和故障
            if any(pattern.search(line) for pattern in self.fault_patterns.values()) or \
               any(level in line_upper for level in ['ERROR', 'FATAL', 'CRITICAL']):
                critical_lines.append(line)
            # 重要5G业务流程
            elif self.compiled_patterns['g5_procedure'].search(line) or \
                 any(nf in line_upper for nf in ['AMF', 'SMF', 'UPF']):
                important_lines.append(line)
            else:
                normal_lines.append(line)

        # 智能采样
        max_lines = self.config.max_lines_per_file

        # 保留所有关键行
        selected_lines = critical_lines[:]
        remaining_quota = max_lines - len(selected_lines)

        if remaining_quota > 0:
            # 保留重要行（采样）
            important_sample_size = min(len(important_lines), remaining_quota // 2)
            if important_sample_size > 0:
                step = max(1, len(important_lines) // important_sample_size)
                selected_lines.extend(important_lines[::step][:important_sample_size])
                remaining_quota -= important_sample_size

            # 保留普通行（采样）
            if remaining_quota > 0 and normal_lines:
                normal_sample_size = min(len(normal_lines), remaining_quota)
                step = max(1, len(normal_lines) // normal_sample_size)
                selected_lines.extend(normal_lines[::step][:normal_sample_size])

        return selected_lines

    def _normalize_timestamps_optimized(self, content: str) -> str:
        """优化的时间戳标准化"""
        # 使用编译的正则表达式
        patterns_replacements = [
            (re.compile(r'(\d{4})-(\d{2})-(\d{2})T(\d{2}):(\d{2}):(\d{2})(?:\.\d+)?(?:Z|[+-]\d{2}:?\d{2})?'),
             r'\1-\2-\3 \4:\5:\6'),
            (re.compile(r'(\d{2})/(\d{2})/(\d{4}) (\d{2}):(\d{2}):(\d{2})'),
             r'\3-\1-\2 \4:\5:\6'),
        ]

        for pattern, replacement in patterns_replacements:
            content = pattern.sub(replacement, content)

        return content

    def _normalize_log_levels_optimized(self, content: str) -> str:
        """优化的日志级别标准化"""
        level_patterns = [
            (re.compile(r'\b(TRACE|DEBUG|INFO|WARN|WARNING|ERROR|FATAL)\s*:', re.IGNORECASE), r'[\1]'),
            (re.compile(r'\b(TRACE|DEBUG|INFO|WARN|WARNING|ERROR|FATAL)\s*-', re.IGNORECASE), r'[\1]'),
            (re.compile(r'<(TRACE|DEBUG|INFO|WARN|WARNING|ERROR|FATAL)>', re.IGNORECASE), r'[\1]'),
        ]

        for pattern, replacement in level_patterns:
            content = pattern.sub(replacement, content)

        return content
