#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
5G核心网日志处理脚本
专为处理188个大规模日志文件设计

使用方法:
python process_5g_logs.py --input-dir /path/to/logs --output-dir /path/to/cleaned --workers 8

特性:
- 多层级进度条显示
- 实时内存监控
- 详细的处理报告
- 并行处理支持
"""

import os
import sys
import time
from pathlib import Path
from progress_enhanced_cleaner import ProgressEnhancedLogCleaner
import argparse
import logging

def setup_logging():
    """设置日志配置"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('5g_log_processing.log'),
            logging.StreamHandler(sys.stdout)
        ]
    )

def validate_input_directory(input_dir: str) -> tuple:
    """验证输入目录并统计文件"""
    input_path = Path(input_dir)
    
    if not input_path.exists():
        raise ValueError(f"输入目录不存在: {input_dir}")
    
    if not input_path.is_dir():
        raise ValueError(f"输入路径不是目录: {input_dir}")
    
    # 查找所有txt文件
    txt_files = list(input_path.glob('*.txt'))
    
    if not txt_files:
        raise ValueError(f"在目录 {input_dir} 中没有找到.txt文件")
    
    # 统计文件大小
    total_size = sum(f.stat().st_size for f in txt_files)
    total_size_gb = total_size / (1024 ** 3)
    
    return txt_files, total_size_gb

def estimate_processing_time(file_count: int, total_size_gb: float, workers: int) -> str:
    """估算处理时间"""
    # 基于经验的估算公式
    # 假设每GB数据需要2-5分钟处理时间（取决于并行度）
    base_time_per_gb = 3.0  # 分钟
    parallel_efficiency = min(workers, 8) * 0.7  # 并行效率
    
    estimated_minutes = (total_size_gb * base_time_per_gb) / parallel_efficiency
    
    if estimated_minutes < 60:
        return f"{estimated_minutes:.0f}分钟"
    else:
        hours = estimated_minutes / 60
        return f"{hours:.1f}小时"

def print_processing_info(txt_files: list, total_size_gb: float, workers: int):
    """打印处理信息"""
    print("=" * 80)
    print("🔥 5G核心网日志处理工具 - 大规模数据处理版")
    print("=" * 80)
    
    print(f"\n📁 数据集信息:")
    print(f"   📄 文件数量: {len(txt_files)} 个")
    print(f"   💾 总数据量: {total_size_gb:.2f} GB")
    print(f"   🔧 并行线程: {workers} 个")
    
    # 显示文件大小分布
    file_sizes = [f.stat().st_size / (1024**2) for f in txt_files]  # MB
    print(f"\n📊 文件大小分布:")
    print(f"   📏 平均大小: {sum(file_sizes)/len(file_sizes):.1f} MB")
    print(f"   📏 最大文件: {max(file_sizes):.1f} MB")
    print(f"   📏 最小文件: {min(file_sizes):.1f} MB")
    
    # 估算处理时间
    estimated_time = estimate_processing_time(len(txt_files), total_size_gb, workers)
    print(f"\n⏱️  预估处理时间: {estimated_time}")
    
    print("\n🚀 即将开始处理...")
    print("=" * 80)

def main():
    parser = argparse.ArgumentParser(
        description='5G核心网日志处理工具 - 专为大规模数据集设计',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 基础处理
  python process_5g_logs.py --input-dir ./logs --output-dir ./cleaned_logs
  
  # 高性能处理（推荐用于188个文件）
  python process_5g_logs.py --input-dir ./logs --output-dir ./cleaned_logs --workers 8
  
  # 内存受限环境
  python process_5g_logs.py --input-dir ./logs --output-dir ./cleaned_logs --workers 2
        """
    )
    
    parser.add_argument('--input-dir', required=True, 
                       help='输入目录路径（包含188个.txt文件）')
    parser.add_argument('--output-dir', required=True, 
                       help='输出目录路径（清洗后的文件）')
    parser.add_argument('--workers', type=int, default=4, 
                       help='并行处理线程数（推荐4-8个，默认4）')
    parser.add_argument('--dry-run', action='store_true', 
                       help='仅分析文件，不执行实际处理')
    parser.add_argument('--resume', action='store_true', 
                       help='跳过已存在的输出文件（断点续传）')
    
    args = parser.parse_args()
    
    # 设置日志
    setup_logging()
    logger = logging.getLogger(__name__)
    
    try:
        # 验证输入目录
        print("🔍 正在分析输入目录...")
        txt_files, total_size_gb = validate_input_directory(args.input_dir)
        
        # 打印处理信息
        print_processing_info(txt_files, total_size_gb, args.workers)
        
        if args.dry_run:
            print("🔍 仅分析模式，不执行实际处理")
            return
        
        # 确认处理
        if len(txt_files) > 50:  # 大量文件时需要确认
            response = input(f"\n⚠️  即将处理 {len(txt_files)} 个文件，总计 {total_size_gb:.2f}GB 数据。继续？(y/N): ")
            if response.lower() != 'y':
                print("❌ 处理已取消")
                return
        
        # 创建输出目录
        output_path = Path(args.output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        # 构建文件列表
        file_list = []
        for txt_file in txt_files:
            output_file = output_path / txt_file.name
            
            # 断点续传：跳过已存在的文件
            if args.resume and output_file.exists():
                logger.info(f"跳过已存在的文件: {txt_file.name}")
                continue
                
            file_list.append((str(txt_file), str(output_file)))
        
        if not file_list:
            print("✅ 所有文件都已处理完成")
            return
        
        print(f"\n🚀 开始处理 {len(file_list)} 个文件...")
        
        # 初始化清洗器
        cleaner = ProgressEnhancedLogCleaner()
        
        # 记录开始时间
        start_time = time.time()
        
        # 执行处理
        results = cleaner.process_files_with_detailed_progress(
            file_list, str(output_path), max_workers=args.workers
        )
        
        # 生成详细报告
        report = cleaner.generate_detailed_report(str(output_path))
        
        # 打印最终摘要
        cleaner.print_final_summary(report)
        
        # 保存处理日志
        logger.info(f"处理完成，详细报告保存在: {output_path / 'processing_report.json'}")
        
        # 如果有失败的文件，提供重试建议
        if results['failed_files'] > 0:
            print(f"\n💡 提示: 有 {results['failed_files']} 个文件处理失败")
            print("   可以使用 --resume 参数重新运行，跳过已成功处理的文件")
        
    except KeyboardInterrupt:
        print("\n⚠️  处理被用户中断")
        logger.info("处理被用户中断")
    except Exception as e:
        print(f"\n❌ 处理失败: {e}")
        logger.exception("处理过程中发生异常")
        sys.exit(1)

if __name__ == "__main__":
    main()
