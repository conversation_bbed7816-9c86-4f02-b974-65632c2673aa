#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试脚本 - 验证稳健版5G日志清洗工具
"""

import os
import tempfile
from pathlib import Path
from robust_5g_cleaner import <PERSON>ustLogCleaner

def create_test_files():
    """创建测试文件"""
    test_dir = Path("test_input")
    test_dir.mkdir(exist_ok=True)
    
    # 创建几个测试文件
    test_files = []
    
    # 文件1: 包含5G网元和错误信息
    content1 = """2024-01-01 10:00:01 [INFO] AMF initialization started
2024-01-01 10:00:02 [ERROR] SMF connection failed: timeout
2024-01-01 10:00:03 [INFO] UPF registration successful
2024-01-01 10:00:04 [FATAL] Authentication failed for SUPI
2024-01-01 10:00:05 [WARN] UDM response delayed
2024-01-01 10:00:06 [INFO] Normal operation log
2024-01-01 10:00:07 [ERROR] PCF policy update failed
2024-01-01 10:00:08 [INFO] Another normal log
2024-01-01 10:00:09 [CRITICAL] NRF service unavailable
2024-01-01 10:00:10 [INFO] System status check"""
    
    file1 = test_dir / "test_file_1.txt"
    with open(file1, 'w', encoding='utf-8') as f:
        f.write(content1)
    test_files.append(file1)
    
    # 文件2: 大量普通日志
    content2 = "\n".join([
        f"2024-01-01 10:{i:02d}:{j:02d} [INFO] Normal log entry {i*60+j}"
        for i in range(10) for j in range(60)
    ])
    content2 += "\n2024-01-01 11:00:00 [ERROR] Important error in large file"
    
    file2 = test_dir / "test_file_2.txt"
    with open(file2, 'w', encoding='utf-8') as f:
        f.write(content2)
    test_files.append(file2)
    
    # 文件3: 空文件
    file3 = test_dir / "test_file_3.txt"
    with open(file3, 'w', encoding='utf-8') as f:
        f.write("")
    test_files.append(file3)
    
    # 文件4: 编码测试
    content4 = """2024-01-01 10:00:01 [INFO] 中文日志测试
2024-01-01 10:00:02 [ERROR] AMF连接失败
2024-01-01 10:00:03 [INFO] SMF正常运行"""
    
    file4 = test_dir / "test_file_4.txt"
    with open(file4, 'w', encoding='utf-8') as f:
        f.write(content4)
    test_files.append(file4)
    
    return test_dir, test_files

def test_memory_monitor():
    """测试内存监控功能"""
    print("🧪 测试内存监控功能...")
    
    cleaner = RobustLogCleaner()
    memory_info = cleaner.memory_monitor.get_memory_info()
    
    print(f"✅ 内存监控正常:")
    print(f"   RSS: {memory_info['rss_gb']:.3f} GB")
    print(f"   系统可用: {memory_info['system_available_gb']:.1f} GB")
    print(f"   内存安全: {cleaner.memory_monitor.is_memory_safe()}")
    
    return True

def test_file_processing():
    """测试文件处理功能"""
    print("\n🧪 测试文件处理功能...")
    
    # 创建测试文件
    test_input_dir, test_files = create_test_files()
    test_output_dir = Path("test_output")
    test_output_dir.mkdir(exist_ok=True)
    
    print(f"创建了 {len(test_files)} 个测试文件")
    
    # 创建清洗器
    cleaner = RobustLogCleaner()
    
    # 处理文件
    results = cleaner.process_files_robust(
        input_dir=str(test_input_dir),
        output_dir=str(test_output_dir)
    )
    
    # 检查结果
    if "error" in results:
        print(f"❌ 处理失败: {results['error']}")
        return False
    
    print(f"✅ 处理完成:")
    print(f"   总文件: {results['total_files']}")
    print(f"   成功处理: {results['newly_processed']}")
    print(f"   失败: {results['failed_files']}")
    
    # 检查输出文件
    output_files = list(test_output_dir.glob("*.txt"))
    print(f"   输出文件: {len(output_files)} 个")
    
    # 检查文件内容
    for output_file in output_files:
        if output_file.stat().st_size > 0:
            with open(output_file, 'r', encoding='utf-8') as f:
                content = f.read()
                lines = content.strip().split('\n')
                print(f"   {output_file.name}: {len(lines)} 行")
    
    return True

def test_resume_functionality():
    """测试断点续传功能"""
    print("\n🧪 测试断点续传功能...")
    
    test_input_dir = Path("test_input")
    test_output_dir = Path("test_output")
    
    if not test_input_dir.exists():
        print("❌ 需要先运行文件处理测试")
        return False
    
    # 第二次运行，应该跳过已处理的文件
    cleaner = RobustLogCleaner()
    results = cleaner.process_files_robust(
        input_dir=str(test_input_dir),
        output_dir=str(test_output_dir)
    )
    
    print(f"✅ 断点续传测试:")
    print(f"   已完成文件: {results['already_completed']}")
    print(f"   新处理文件: {results['newly_processed']}")
    
    if results['newly_processed'] == 0 and results['already_completed'] > 0:
        print("✅ 断点续传功能正常")
        return True
    else:
        print("⚠️  断点续传可能有问题")
        return False

def cleanup_test_files():
    """清理测试文件"""
    print("\n🧹 清理测试文件...")
    
    import shutil
    
    for dir_name in ["test_input", "test_output"]:
        dir_path = Path(dir_name)
        if dir_path.exists():
            shutil.rmtree(dir_path)
            print(f"   删除: {dir_name}")

def main():
    """主测试函数"""
    print("🚀 稳健版5G日志清洗工具 - 快速测试")
    print("="*50)
    
    try:
        # 测试内存监控
        if not test_memory_monitor():
            print("❌ 内存监控测试失败")
            return
        
        # 测试文件处理
        if not test_file_processing():
            print("❌ 文件处理测试失败")
            return
        
        # 测试断点续传
        if not test_resume_functionality():
            print("⚠️  断点续传测试有问题，但不影响基本功能")
        
        print("\n🎉 所有测试完成!")
        print("✅ 稳健版工具可以正常使用")
        
        # 询问是否清理测试文件
        try:
            response = input("\n是否清理测试文件? (y/N): ")
            if response.lower() == 'y':
                cleanup_test_files()
        except KeyboardInterrupt:
            pass
        
    except Exception as e:
        print(f"❌ 测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
