#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
5G核心网日志清洗工具 - 内存优化单文件版本
专为16GB内存、2核CPU服务器环境优化

特性:
- 内存安全处理，避免OOM
- 针对2核CPU优化的并行策略
- 简化进度显示，减少内存开销
- 单文件部署，便于服务器使用
- 专为188个大规模日志文件设计

使用方法:
python memory_optimized_5g_cleaner.py /path/to/input /path/to/output
"""

import os
import sys
import gc
import time
import psutil
import threading
from pathlib import Path
from typing import List, Dict, Tuple, Optional
from collections import deque
from datetime import datetime
import re
import json
import argparse

# 内存安全配置
MEMORY_LIMIT_GB = 12.0  # 16GB的75%，留出安全余量
MEMORY_WARNING_GB = 10.0  # 10GB时开始警告
MAX_WORKERS = 2  # 针对2核CPU优化
CHUNK_SIZE = 512 * 1024  # 512KB chunks，减少内存占用
MAX_LINES_PER_FILE = 3000  # 减少每文件保留行数
MAX_CHARS_PER_FILE = 300000  # 减少每文件字符数

class MemoryMonitor:
    """轻量级内存监控器"""
    def __init__(self):
        self.process = psutil.Process()
        self.peak_memory = 0
        
    def get_memory_gb(self) -> float:
        """获取当前内存使用量(GB)"""
        try:
            memory_gb = self.process.memory_info().rss / (1024**3)
            self.peak_memory = max(self.peak_memory, memory_gb)
            return memory_gb
        except:
            return 0.0
    
    def check_memory_safe(self) -> bool:
        """检查内存是否安全"""
        current_memory = self.get_memory_gb()
        if current_memory > MEMORY_LIMIT_GB:
            print(f"⚠️  内存使用过高: {current_memory:.1f}GB > {MEMORY_LIMIT_GB}GB")
            return False
        elif current_memory > MEMORY_WARNING_GB:
            print(f"⚠️  内存警告: {current_memory:.1f}GB")
        return True
    
    def force_gc(self):
        """强制垃圾回收"""
        gc.collect()

class OptimizedLogCleaner:
    """内存优化的5G日志清洗器"""
    
    def __init__(self):
        self.memory_monitor = MemoryMonitor()
        self.stats = {
            'total_files': 0,
            'processed_files': 0,
            'failed_files': 0,
            'total_lines_processed': 0,
            'total_lines_kept': 0,
            'start_time': None,
            'failed_files_list': []
        }
        
        # 预编译正则表达式（内存友好）
        self._compile_patterns()
    
    def _compile_patterns(self):
        """编译关键的正则表达式模式"""
        # 5G网元（精简版）
        g5_nf = 'AMF|SMF|UPF|AUSF|UDM|UDR|PCF|NRF|NSSF|NEF'
        
        self.patterns = {
            'g5_nf': re.compile(rf'\b({g5_nf})\b', re.IGNORECASE),
            'error': re.compile(r'\[(ERROR|FATAL|CRITICAL)\]', re.IGNORECASE),
            'warning': re.compile(r'\[(WARN|WARNING)\]', re.IGNORECASE),
            'timestamp': re.compile(r'\d{4}-\d{2}-\d{2}[T ]\d{2}:\d{2}:\d{2}'),
            'fault_keywords': re.compile(
                r'(failed|failure|error|timeout|exception|denied|reject|abort|'
                r'connection.*failed|authentication.*failed|resource.*exhausted)',
                re.IGNORECASE
            )
        }
    
    def _detect_encoding_fast(self, file_path: str) -> str:
        """快速编码检测"""
        try:
            # 只读取前1KB进行检测
            with open(file_path, 'rb') as f:
                sample = f.read(1024)
            
            # 优先尝试UTF-8
            try:
                sample.decode('utf-8')
                return 'utf-8'
            except UnicodeDecodeError:
                pass
            
            # 尝试GBK
            try:
                sample.decode('gbk')
                return 'gbk'
            except UnicodeDecodeError:
                pass
                
            return 'latin1'  # 最后的备选
        except:
            return 'utf-8'
    
    def _read_file_in_chunks(self, file_path: str) -> Optional[str]:
        """分块读取文件，内存安全"""
        try:
            encoding = self._detect_encoding_fast(file_path)
            file_size = os.path.getsize(file_path)
            
            # 对于超大文件，限制读取大小
            if file_size > 50 * 1024 * 1024:  # 50MB
                print(f"   大文件 {Path(file_path).name} ({file_size/1024/1024:.1f}MB)，限制读取")
                return self._read_large_file_limited(file_path, encoding)
            
            # 正常读取
            with open(file_path, 'r', encoding=encoding, buffering=CHUNK_SIZE) as f:
                return f.read()
                
        except Exception as e:
            print(f"   读取失败 {Path(file_path).name}: {e}")
            return None
    
    def _read_large_file_limited(self, file_path: str, encoding: str) -> str:
        """限制读取大文件的前部分内容"""
        content_parts = []
        total_read = 0
        max_read = 30 * 1024 * 1024  # 最多读取30MB
        
        try:
            with open(file_path, 'r', encoding=encoding) as f:
                while total_read < max_read:
                    chunk = f.read(CHUNK_SIZE)
                    if not chunk:
                        break
                    content_parts.append(chunk)
                    total_read += len(chunk.encode(encoding))
                    
                    # 内存检查
                    if not self.memory_monitor.check_memory_safe():
                        break
            
            return ''.join(content_parts)
        except Exception as e:
            print(f"   大文件读取失败: {e}")
            return ""
    
    def _intelligent_line_sampling(self, lines: List[str]) -> List[str]:
        """内存优化的智能行采样"""
        if len(lines) <= MAX_LINES_PER_FILE:
            return [line.strip() for line in lines if line.strip()]
        
        # 快速分类（避免复杂操作）
        critical_lines = []
        important_lines = []
        normal_lines = []
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            # 关键错误
            if self.patterns['error'].search(line) or self.patterns['fault_keywords'].search(line):
                critical_lines.append(line)
            # 5G网元相关
            elif self.patterns['g5_nf'].search(line) or self.patterns['warning'].search(line):
                important_lines.append(line)
            else:
                normal_lines.append(line)
        
        # 智能采样
        selected_lines = critical_lines[:]  # 保留所有关键行
        remaining_quota = MAX_LINES_PER_FILE - len(selected_lines)
        
        if remaining_quota > 0:
            # 重要行采样
            important_count = min(len(important_lines), remaining_quota // 2)
            if important_count > 0:
                step = max(1, len(important_lines) // important_count)
                selected_lines.extend(important_lines[::step][:important_count])
                remaining_quota -= important_count
            
            # 普通行采样
            if remaining_quota > 0 and normal_lines:
                normal_count = min(len(normal_lines), remaining_quota)
                step = max(1, len(normal_lines) // normal_count)
                selected_lines.extend(normal_lines[::step][:normal_count])
        
        return selected_lines
    
    def _clean_content(self, content: str) -> str:
        """清洗日志内容"""
        if not content or not content.strip():
            return ""
        
        # 1. 基础清理
        content = re.sub(r'\r\n|\r', '\n', content)
        content = re.sub(r'[\x00-\x08\x0b\x0c\x0e-\x1f\x7f-\xff]', '', content)
        
        # 2. 行处理
        lines = content.split('\n')
        original_count = len(lines)
        
        # 智能采样
        cleaned_lines = self._intelligent_line_sampling(lines)
        
        # 3. 重新组合
        content = '\n'.join(cleaned_lines)
        
        # 4. 限制字符数
        if len(content) > MAX_CHARS_PER_FILE:
            content = content[:MAX_CHARS_PER_FILE]
            last_newline = content.rfind('\n')
            if last_newline > 0:
                content = content[:last_newline]
        
        # 5. 简单标准化
        content = re.sub(
            r'(\d{4})-(\d{2})-(\d{2})T(\d{2}):(\d{2}):(\d{2})(?:\.\d+)?(?:Z|[+-]\d{2}:?\d{2})?',
            r'\1-\2-\3 \4:\5:\6', content
        )
        
        # 更新统计
        self.stats['total_lines_processed'] += original_count
        self.stats['total_lines_kept'] += len(cleaned_lines)
        
        return content
    
    def _process_single_file(self, input_file: str, output_file: str) -> bool:
        """处理单个文件"""
        try:
            # 内存检查
            if not self.memory_monitor.check_memory_safe():
                self.memory_monitor.force_gc()
                time.sleep(0.1)  # 短暂等待
            
            # 读取文件
            content = self._read_file_in_chunks(input_file)
            if not content:
                return False
            
            # 清洗内容
            cleaned_content = self._clean_content(content)
            
            # 释放原始内容内存
            del content
            
            if cleaned_content.strip():
                # 创建输出目录
                os.makedirs(os.path.dirname(output_file), exist_ok=True)
                
                # 保存文件
                with open(output_file, 'w', encoding='utf-8') as f:
                    f.write(cleaned_content)
                
                # 释放清洗内容内存
                del cleaned_content
                
                return True
            else:
                del cleaned_content
                return False
                
        except Exception as e:
            print(f"   处理失败: {e}")
            self.stats['failed_files_list'].append(f"{Path(input_file).name}: {str(e)}")
            return False
        finally:
            # 强制垃圾回收
            self.memory_monitor.force_gc()
    
    def process_files(self, file_list: List[Tuple[str, str]]) -> Dict:
        """处理文件列表"""
        self.stats['total_files'] = len(file_list)
        self.stats['start_time'] = time.time()
        
        print(f"🚀 开始处理 {len(file_list)} 个文件")
        print(f"💾 内存限制: {MEMORY_LIMIT_GB}GB，当前: {self.memory_monitor.get_memory_gb():.1f}GB")
        
        # 顺序处理（避免并发内存压力）
        for i, (input_file, output_file) in enumerate(file_list, 1):
            file_name = Path(input_file).name
            
            # 显示进度
            progress = i / len(file_list) * 100
            memory_gb = self.memory_monitor.get_memory_gb()
            elapsed = time.time() - self.stats['start_time']
            
            print(f"[{i:3d}/{len(file_list)}] {progress:5.1f}% | {file_name} | "
                  f"内存: {memory_gb:.1f}GB | 用时: {elapsed/60:.1f}分钟")
            
            # 处理文件
            if self._process_single_file(input_file, output_file):
                self.stats['processed_files'] += 1
            else:
                self.stats['failed_files'] += 1
            
            # 每10个文件强制垃圾回收
            if i % 10 == 0:
                self.memory_monitor.force_gc()
                print(f"   内存清理后: {self.memory_monitor.get_memory_gb():.1f}GB")
        
        return self._get_results()
    
    def _get_results(self) -> Dict:
        """获取处理结果"""
        elapsed_time = time.time() - self.stats['start_time']
        
        return {
            'total_files': self.stats['total_files'],
            'processed_files': self.stats['processed_files'],
            'failed_files': self.stats['failed_files'],
            'total_lines_processed': self.stats['total_lines_processed'],
            'total_lines_kept': self.stats['total_lines_kept'],
            'processing_time': elapsed_time,
            'peak_memory_gb': self.memory_monitor.peak_memory,
            'failed_files_list': self.stats['failed_files_list']
        }
    
    def print_summary(self, results: Dict):
        """打印处理摘要"""
        print("\n" + "="*60)
        print("🎉 处理完成!")
        print("="*60)
        
        success_rate = results['processed_files'] / results['total_files'] * 100
        line_retention = results['total_lines_kept'] / results['total_lines_processed'] * 100 if results['total_lines_processed'] > 0 else 0
        
        print(f"📊 处理结果:")
        print(f"   总文件: {results['total_files']}")
        print(f"   成功: {results['processed_files']} ({success_rate:.1f}%)")
        print(f"   失败: {results['failed_files']}")
        
        print(f"\n📈 数据统计:")
        print(f"   处理行数: {results['total_lines_processed']:,}")
        print(f"   保留行数: {results['total_lines_kept']:,}")
        print(f"   保留率: {line_retention:.1f}%")
        
        print(f"\n⚡ 性能:")
        print(f"   处理时间: {results['processing_time']/60:.1f} 分钟")
        print(f"   峰值内存: {results['peak_memory_gb']:.1f}GB")
        print(f"   平均速度: {results['processed_files']/(results['processing_time']/60):.1f} 文件/分钟")
        
        if results['failed_files'] > 0:
            print(f"\n❌ 失败文件:")
            for failed in results['failed_files_list'][:5]:  # 只显示前5个
                print(f"   {failed}")
            if len(results['failed_files_list']) > 5:
                print(f"   ... 还有 {len(results['failed_files_list'])-5} 个")
        
        print("="*60)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='5G核心网日志清洗工具 - 内存优化版')
    parser.add_argument('input_dir', help='输入目录路径')
    parser.add_argument('output_dir', help='输出目录路径')
    parser.add_argument('--pattern', default='*.txt', help='文件匹配模式 (默认: *.txt)')
    
    args = parser.parse_args()
    
    # 验证输入目录
    input_path = Path(args.input_dir)
    if not input_path.exists():
        print(f"❌ 输入目录不存在: {args.input_dir}")
        sys.exit(1)
    
    # 查找文件
    txt_files = list(input_path.glob(args.pattern))
    if not txt_files:
        print(f"❌ 在 {args.input_dir} 中没有找到匹配 {args.pattern} 的文件")
        sys.exit(1)
    
    # 创建输出目录
    output_path = Path(args.output_dir)
    output_path.mkdir(parents=True, exist_ok=True)
    
    # 构建文件列表
    file_list = [(str(f), str(output_path / f.name)) for f in txt_files]
    
    print(f"🔍 找到 {len(txt_files)} 个文件")
    
    # 系统信息
    memory_total = psutil.virtual_memory().total / (1024**3)
    cpu_count = psutil.cpu_count()
    print(f"💻 系统: {memory_total:.1f}GB内存, {cpu_count}核CPU")
    print(f"⚙️  配置: 内存限制{MEMORY_LIMIT_GB}GB, 顺序处理")
    
    # 确认处理
    if len(txt_files) > 10:
        response = input(f"\n继续处理 {len(txt_files)} 个文件? (y/N): ")
        if response.lower() != 'y':
            print("❌ 已取消")
            sys.exit(0)
    
    # 开始处理
    cleaner = OptimizedLogCleaner()
    results = cleaner.process_files(file_list)
    cleaner.print_summary(results)


if __name__ == "__main__":
    main()
