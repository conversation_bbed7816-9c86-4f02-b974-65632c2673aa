#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的5G核心网日志故障分类解决方案
"""

import os
import tempfile
from pathlib import Path
from collections import Counter
import numpy as np

def create_test_data():
    """创建测试数据来验证标签提取功能"""
    
    # 创建临时目录
    train_dir = Path("test_train")
    test_dir = Path("test_test")
    
    train_dir.mkdir(exist_ok=True)
    test_dir.mkdir(exist_ok=True)
    
    # 创建训练数据 - 模拟真实的5G日志文件
    train_samples = [
        # 正常日志 (类别0) - 68个样本
        *[(0, f"0_{i}.txt", "2024-01-01 10:00:01 [INFO][AMF][NGAP] Normal operation\n2024-01-01 10:00:02 [INFO][SMF] Session established successfully") for i in range(1, 69)],
        
        # 故障类型1 - 16个样本
        *[(1, f"1_{i}.txt", "2024-01-01 10:00:01 [ERROR][AMF][NGAP] Connection failed\n2024-01-01 10:00:02 [ERROR] Authentication timeout") for i in range(1, 17)],
        
        # 故障类型2 - 18个样本  
        *[(2, f"2_{i}.txt", "2024-01-01 10:00:01 [ERROR][SMF] Session establishment failed\n2024-01-01 10:00:02 [FATAL] Resource unavailable") for i in range(1, 19)],
        
        # 故障类型3 - 19个样本
        *[(3, f"3_{i}.txt", "2024-01-01 10:00:01 [ERROR][UPF] Data plane failure\n2024-01-01 10:00:02 [ERROR] Packet loss detected") for i in range(1, 20)],
        
        # 故障类型4 - 8个样本 (最少的类别)
        *[(4, f"4_{i}.txt", "2024-01-01 10:00:01 [CRITICAL][AUSF] Authentication server down\n2024-01-01 10:00:02 [FATAL] Service unavailable") for i in range(1, 9)],
        
        # 故障类型5 - 25个样本
        *[(5, f"5_{i}.txt", "2024-01-01 10:00:01 [ERROR][UDM] Database connection lost\n2024-01-01 10:00:02 [ERROR] User data access failed") for i in range(1, 26)],
        
        # 故障类型6 - 16个样本
        *[(6, f"6_{i}.txt", "2024-01-01 10:00:01 [ERROR][PCF] Policy update failed\n2024-01-01 10:00:02 [WARN] Configuration error") for i in range(1, 17)],
        
        # 故障类型7 - 18个样本
        *[(7, f"7_{i}.txt", "2024-01-01 10:00:01 [ERROR][NRF] Service discovery failed\n2024-01-01 10:00:02 [ERROR] Network function unreachable") for i in range(1, 19)]
    ]
    
    # 写入训练文件
    for label, filename, content in train_samples:
        file_path = train_dir / filename
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
    
    # 创建测试数据 - 100个文件
    test_samples = [
        (f"{i}.txt", f"2024-01-01 10:00:01 [INFO][AMF] Test log {i}\n2024-01-01 10:00:02 [INFO] Processing request")
        for i in range(1, 101)
    ]
    
    # 写入测试文件
    for filename, content in test_samples:
        file_path = test_dir / filename
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
    
    print(f"✅ 创建测试数据:")
    print(f"   训练文件: {len(train_samples)} 个")
    print(f"   测试文件: {len(test_samples)} 个")
    
    return str(train_dir), str(test_dir)

def test_label_extraction():
    """测试标签提取功能"""
    print("\n🧪 测试标签提取功能...")
    
    from 5g_fault_classification_solution import G5FaultClassifier
    
    # 创建测试数据
    train_dir, test_dir = create_test_data()
    
    # 创建分类器
    classifier = G5FaultClassifier()
    
    # 加载数据
    classifier.load_data(train_dir, test_dir)
    
    # 检查标签分布
    expected_distribution = {0: 68, 1: 16, 2: 18, 3: 19, 4: 8, 5: 25, 6: 16, 7: 18}
    actual_distribution = Counter(classifier.y_train)
    
    print(f"期望分布: {expected_distribution}")
    print(f"实际分布: {dict(actual_distribution)}")
    
    # 验证分布是否正确
    success = True
    for label, expected_count in expected_distribution.items():
        actual_count = actual_distribution.get(label, 0)
        if actual_count != expected_count:
            print(f"❌ 标签 {label}: 期望 {expected_count}, 实际 {actual_count}")
            success = False
    
    if success:
        print("✅ 标签提取测试通过!")
    else:
        print("❌ 标签提取测试失败!")
    
    return success, classifier

def test_class_imbalance_handling(classifier):
    """测试类别不平衡处理"""
    print("\n🧪 测试类别不平衡处理...")
    
    try:
        # 预处理特征
        classifier.preprocess_features()
        
        # 处理类别不平衡
        classifier.handle_class_imbalance()
        
        print("✅ 类别不平衡处理测试通过!")
        return True
        
    except Exception as e:
        print(f"❌ 类别不平衡处理测试失败: {e}")
        return False

def test_model_training(classifier):
    """测试模型训练"""
    print("\n🧪 测试模型训练...")
    
    try:
        # 训练模型
        classifier.train_models()
        
        print("✅ 模型训练测试通过!")
        return True
        
    except Exception as e:
        print(f"❌ 模型训练测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_prediction_and_output(classifier):
    """测试预测和输出"""
    print("\n🧪 测试预测和输出...")
    
    try:
        # 预测
        predictions, probabilities = classifier.predict(use_ensemble=True)
        
        # 检查预测结果
        print(f"预测数量: {len(predictions)}")
        print(f"预测分布: {Counter(predictions)}")
        
        # 检查预测值范围
        if all(0 <= pred <= 7 for pred in predictions):
            print("✅ 预测值范围正确 (0-7)")
        else:
            print("❌ 预测值超出范围")
            return False
        
        # 保存结果
        classifier.save_results(predictions, 'test_result.csv')
        
        # 检查输出文件
        if Path('test_result.csv').exists():
            print("✅ 结果文件生成成功!")
            
            # 读取并验证文件内容
            import pandas as pd
            try:
                df = pd.read_csv('test_result.csv', encoding='gb2312')
                print(f"输出文件包含 {len(df)} 行")
                print("前5行:")
                print(df.head())
                
                # 检查列名
                expected_columns = ['日志片段文件编号', '故障类型']
                if list(df.columns) == expected_columns:
                    print("✅ 输出格式正确!")
                else:
                    print(f"❌ 输出格式错误: {list(df.columns)}")
                    return False
                    
            except Exception as e:
                print(f"⚠️  GB2312读取失败，尝试UTF-8: {e}")
                df = pd.read_csv('test_result.csv', encoding='utf-8')
                print("✅ UTF-8格式读取成功!")
        else:
            print("❌ 结果文件生成失败!")
            return False
        
        print("✅ 预测和输出测试通过!")
        return True
        
    except Exception as e:
        print(f"❌ 预测和输出测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def cleanup_test_data():
    """清理测试数据"""
    print("\n🧹 清理测试数据...")
    
    import shutil
    
    for dir_name in ["test_train", "test_test"]:
        dir_path = Path(dir_name)
        if dir_path.exists():
            shutil.rmtree(dir_path)
            print(f"   删除: {dir_name}")
    
    # 删除测试结果文件
    for file_name in ["test_result.csv"]:
        file_path = Path(file_name)
        if file_path.exists():
            file_path.unlink()
            print(f"   删除: {file_name}")

def main():
    """主测试函数"""
    print("🚀 测试修复后的5G核心网日志故障分类解决方案")
    print("="*60)
    
    try:
        # 测试1: 标签提取
        success1, classifier = test_label_extraction()
        if not success1:
            print("❌ 标签提取测试失败，停止后续测试")
            return
        
        # 测试2: 类别不平衡处理
        success2 = test_class_imbalance_handling(classifier)
        if not success2:
            print("❌ 类别不平衡处理测试失败，停止后续测试")
            return
        
        # 测试3: 模型训练
        success3 = test_model_training(classifier)
        if not success3:
            print("❌ 模型训练测试失败，停止后续测试")
            return
        
        # 测试4: 预测和输出
        success4 = test_prediction_and_output(classifier)
        
        # 总结
        print("\n" + "="*60)
        if all([success1, success2, success3, success4]):
            print("🎉 所有测试通过! 修复后的解决方案可以正常工作!")
        else:
            print("⚠️  部分测试失败，需要进一步调试")
        
    except Exception as e:
        print(f"❌ 测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理测试数据
        try:
            response = input("\n是否清理测试数据? (y/N): ")
            if response.lower() == 'y':
                cleanup_test_data()
        except KeyboardInterrupt:
            pass

if __name__ == "__main__":
    main()
