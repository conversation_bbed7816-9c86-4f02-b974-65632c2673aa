#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
5G核心网日志清洗工具 - 增强版本
专为故障分类任务优化

主要改进：
1. 内存效率优化和并行处理
2. 完善的5G网元和故障模式识别
3. 智能的日志采样策略
4. 增强的特征提取
5. 完善的错误处理和配置管理
"""

import os
import sys
import argparse
from pathlib import Path
from optimized_cleaner import OptimizedMemoryFriendlyLogCleaner, CleaningConfig
import logging
import json
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('log_cleaning.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

def create_optimized_config(args) -> CleaningConfig:
    """根据命令行参数创建优化配置"""
    return CleaningConfig(
        max_lines_per_file=args.max_lines,
        max_chars_per_file=args.max_chars,
        max_workers=args.workers,
        enable_parallel_processing=args.parallel,
        preserve_error_logs=True,
        
        # 扩展的5G网元列表
        g5_network_functions=[
            # 核心控制面网元
            'AMF', 'SMF', 'AUSF', 'UDM', 'UDR', 'PCF', 'NRF', 'NSSF', 'NEF',
            # 用户面网元
            'UPF', 'N3IWF',
            # 边缘和专网网元
            'SEPP', 'SCP', 'BSF', 'CHF', 'NWDAF', 'AF', 'GMLC', 'LMF', 'UDSF', 'UNEF',
            # 接入网相关
            'gNB', 'ng-eNB', 'CU', 'DU', 'RU'
        ],
        
        # 5G接口和协议
        g5_interfaces=[
            # N接口
            'N1', 'N2', 'N3', 'N4', 'N6', 'N7', 'N8', 'N9', 'N10', 'N11', 'N12', 'N13', 'N14', 'N15', 'N16', 'N17',
            # 服务化接口
            'Namf', 'Nsmf', 'Nupf', 'Nausf', 'Nudm', 'Nudr', 'Npcf', 'Nnrf', 'Nnssf', 'Nnef',
            # 协议
            'NGAP', 'HTTP2', 'SBI', 'PFCP', 'GTP-U', 'SCTP', 'DIAMETER', 'RADIUS'
        ],
        
        # 5G业务流程
        g5_procedures=[
            'Registration', 'Deregistration', 'Authentication', 'Authorization', 
            'Handover', 'PDU_Session_Establishment', 'PDU_Session_Modification', 
            'PDU_Session_Release', 'Service_Request', 'UE_Context_Transfer',
            'Path_Switch', 'Xn_Handover', 'N2_Handover', 'Inter_RAT_Handover'
        ]
    )

def analyze_data_quality(cleaner: OptimizedMemoryFriendlyLogCleaner, data_dir: str):
    """分析数据质量"""
    logger.info("开始数据质量分析...")
    
    # 分析故障分布
    fault_distribution = cleaner.analyze_fault_distribution(data_dir)
    
    logger.info("故障类型分布:")
    for fault_type, stats in fault_distribution.items():
        logger.info(f"  {fault_type}: {stats.get('total_files', 0)} 个文件")
        
        # 显示主要故障模式
        fault_patterns = {k: v for k, v in stats.items() if k.startswith('fault_') and v > 0}
        if fault_patterns:
            logger.info(f"    主要故障模式: {fault_patterns}")
    
    return fault_distribution

def process_dataset(cleaner: OptimizedMemoryFriendlyLogCleaner, 
                   input_dir: str, output_dir: str, dataset_type: str):
    """处理数据集"""
    logger.info(f"开始处理{dataset_type}数据集...")
    
    input_path = Path(input_dir)
    output_path = Path(output_dir)
    output_path.mkdir(parents=True, exist_ok=True)
    
    # 收集文件列表
    file_list = []
    
    if dataset_type == 'train':
        # 处理训练数据
        label_file = input_path / 'label.csv'
        if label_file.exists():
            import pandas as pd
            labels_df = pd.read_csv(label_file)
            
            # 复制标签文件
            labels_df.to_csv(output_path / 'label.csv', index=False)
            
            for _, row in labels_df.iterrows():
                file_id = int(row['日志片段文件编号'])
                input_file = input_path / f'{file_id}.txt'
                output_file = output_path / f'{file_id}.txt'
                
                if input_file.exists():
                    file_list.append((str(input_file), str(output_file)))
        else:
            logger.warning(f"标签文件不存在: {label_file}")
    
    else:
        # 处理测试数据
        for txt_file in input_path.glob('*.txt'):
            output_file = output_path / txt_file.name
            file_list.append((str(txt_file), str(output_file)))
    
    logger.info(f"找到 {len(file_list)} 个文件需要处理")
    
    # 并行处理文件
    results = cleaner.process_files_parallel(file_list, str(output_path))
    
    logger.info(f"{dataset_type}数据集处理完成:")
    logger.info(f"  成功处理: {results['processed']} 个文件")
    logger.info(f"  处理失败: {results['failed']} 个文件")
    logger.info(f"  空文件: {results['empty']} 个文件")
    
    return results

def extract_and_save_features(cleaner: OptimizedMemoryFriendlyLogCleaner, 
                             data_dir: str, output_file: str):
    """提取并保存特征"""
    logger.info("开始提取增强特征...")
    
    features_data = []
    data_path = Path(data_dir)
    
    # 处理训练数据
    label_file = data_path / 'label.csv'
    if label_file.exists():
        import pandas as pd
        labels_df = pd.read_csv(label_file)
        
        for _, row in labels_df.iterrows():
            file_id = int(row['日志片段文件编号'])
            fault_type = row['故障类型']
            file_path = data_path / f'{file_id}.txt'
            
            if file_path.exists():
                content = cleaner._read_file_efficiently(str(file_path))
                if content:
                    features = cleaner.extract_enhanced_features(content)
                    features['file_id'] = file_id
                    features['fault_type'] = fault_type
                    features_data.append(features)
    
    # 保存特征
    if features_data:
        import pandas as pd
        features_df = pd.DataFrame(features_data)
        features_df.to_csv(output_file, index=False)
        logger.info(f"特征已保存到: {output_file}")
        logger.info(f"特征维度: {len(features_df.columns)} 维")
        logger.info(f"样本数量: {len(features_df)} 个")
    
    return features_data

def main():
    parser = argparse.ArgumentParser(description='5G核心网日志清洗工具 - 增强版')
    parser.add_argument('--train-dir', default='files/train', help='训练数据目录')
    parser.add_argument('--test-dir', default='files/test', help='测试数据目录')
    parser.add_argument('--output-dir', default='files_cleaned', help='输出目录')
    parser.add_argument('--max-lines', type=int, default=5000, help='每个文件最大行数')
    parser.add_argument('--max-chars', type=int, default=500000, help='每个文件最大字符数')
    parser.add_argument('--workers', type=int, default=4, help='并行处理线程数')
    parser.add_argument('--parallel', action='store_true', help='启用并行处理')
    parser.add_argument('--analyze-only', action='store_true', help='仅分析数据质量')
    parser.add_argument('--extract-features', action='store_true', help='提取增强特征')
    
    args = parser.parse_args()
    
    # 创建优化配置
    config = create_optimized_config(args)
    
    # 初始化清洗器
    cleaner = OptimizedMemoryFriendlyLogCleaner(
        train_dir=args.train_dir,
        test_dir=args.test_dir,
        config=config
    )
    
    logger.info("5G核心网日志清洗工具启动")
    logger.info(f"配置: 最大行数={config.max_lines_per_file}, "
                f"最大字符数={config.max_chars_per_file}, "
                f"并行线程={config.max_workers}")
    
    start_time = datetime.now()
    
    try:
        # 数据质量分析
        if Path(args.train_dir).exists():
            fault_distribution = analyze_data_quality(cleaner, args.train_dir)
        
        if args.analyze_only:
            logger.info("仅分析模式，跳过数据清洗")
            return
        
        # 创建输出目录
        output_path = Path(args.output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        # 处理训练数据
        if Path(args.train_dir).exists():
            train_results = process_dataset(
                cleaner, args.train_dir, 
                str(output_path / 'train'), 'train'
            )
        
        # 处理测试数据
        if Path(args.test_dir).exists():
            test_results = process_dataset(
                cleaner, args.test_dir, 
                str(output_path / 'test'), 'test'
            )
        
        # 提取增强特征
        if args.extract_features and Path(args.train_dir).exists():
            features_file = output_path / 'enhanced_features.csv'
            extract_and_save_features(cleaner, str(output_path / 'train'), str(features_file))
        
        # 生成清洗报告
        report = cleaner.generate_cleaning_report(str(output_path))
        
        end_time = datetime.now()
        processing_time = (end_time - start_time).total_seconds()
        
        logger.info(f"数据清洗完成！")
        logger.info(f"总处理时间: {processing_time:.2f} 秒")
        logger.info(f"输出目录: {args.output_dir}")
        
    except Exception as e:
        logger.error(f"处理过程中发生错误: {e}")
        raise

if __name__ == "__main__":
    main()
